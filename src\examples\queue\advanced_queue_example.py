import multiprocessing
import time
import random
import json
from datetime import datetime
from enum import Enum


class MessageType(Enum):
    """消息类型枚举"""

    DATA = "data"
    COMMAND = "command"
    STOP = "stop"


class TaskStatus(Enum):
    """任务状态枚举"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


def producer_process(task_queue, result_queue, producer_id, num_tasks=5):
    """
    生产者进程：生成任务并放入任务队列
    """
    print(f"[生产者-{producer_id}] 启动，将生成 {num_tasks} 个任务")

    for i in range(num_tasks):
        task = {
            "task_id": f"P{producer_id}-T{i+1}",
            "producer_id": producer_id,
            "message_type": MessageType.DATA.value,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "operation": random.choice(["add", "multiply", "power"]),
                "numbers": [random.randint(1, 100) for _ in range(2)],
                "priority": random.randint(1, 5),
            },
            "status": TaskStatus.PENDING.value,
        }

        task_queue.put(task)
        print(
            f"[生产者-{producer_id}] 生成任务: {task['task_id']}, 操作: {task['data']['operation']}"
        )

        # 随机等待
        time.sleep(random.uniform(0.5, 2.0))

    print(f"[生产者-{producer_id}] 完成任务生成")


def consumer_process(task_queue, result_queue, consumer_id):
    """
    消费者进程：从任务队列获取任务并处理
    """
    print(f"[消费者-{consumer_id}] 启动，等待处理任务")

    processed_tasks = 0

    while True:
        try:
            # 从任务队列获取任务
            task = task_queue.get(timeout=5)

            if task is None or task.get("message_type") == MessageType.STOP.value:
                print(f"[消费者-{consumer_id}] 收到停止信号")
                break

            # 更新任务状态
            task["status"] = TaskStatus.PROCESSING.value
            task["consumer_id"] = consumer_id
            task["start_time"] = datetime.now().isoformat()

            print(f"[消费者-{consumer_id}] 开始处理任务: {task['task_id']}")

            # 模拟处理时间
            processing_time = random.uniform(1, 3)
            time.sleep(processing_time)

            # 执行具体操作
            try:
                operation = task["data"]["operation"]
                numbers = task["data"]["numbers"]

                if operation == "add":
                    result = sum(numbers)
                elif operation == "multiply":
                    result = numbers[0] * numbers[1]
                elif operation == "power":
                    result = numbers[0] ** numbers[1]
                else:
                    raise ValueError(f"未知操作: {operation}")

                # 更新任务结果
                task["status"] = TaskStatus.COMPLETED.value
                task["result"] = result
                task["end_time"] = datetime.now().isoformat()
                task["processing_time"] = processing_time

                print(
                    f"[消费者-{consumer_id}] 任务 {task['task_id']} 处理完成: {numbers} {operation} = {result}"
                )

            except Exception as e:
                task["status"] = TaskStatus.FAILED.value
                task["error"] = str(e)
                task["end_time"] = datetime.now().isoformat()
                print(f"[消费者-{consumer_id}] 任务 {task['task_id']} 处理失败: {e}")

            # 将结果放入结果队列
            result_queue.put(task)
            processed_tasks += 1

        except multiprocessing.TimeoutError:
            print(f"[消费者-{consumer_id}] 等待任务超时，继续等待...")
        except Exception as e:
            print(f"[消费者-{consumer_id}] 处理过程中出错: {e}")

    print(f"[消费者-{consumer_id}] 结束，总共处理了 {processed_tasks} 个任务")


def result_collector_process(result_queue, num_expected_results):
    """
    结果收集进程：收集所有处理结果
    """
    print(f"[结果收集器] 启动，期待收集 {num_expected_results} 个结果")

    results = []
    collected_count = 0

    while collected_count < num_expected_results:
        try:
            result = result_queue.get(timeout=10)
            results.append(result)
            collected_count += 1

            status = result["status"]
            task_id = result["task_id"]

            if status == TaskStatus.COMPLETED.value:
                print(f"[结果收集器] 收到成功结果: {task_id}, 结果: {result['result']}")
            else:
                print(
                    f"[结果收集器] 收到失败结果: {task_id}, 错误: {result.get('error', '未知错误')}"
                )

        except multiprocessing.TimeoutError:
            print(f"[结果收集器] 等待结果超时...")
            break
        except Exception as e:
            print(f"[结果收集器] 收集结果时出错: {e}")
            break

    # 统计结果
    completed = len([r for r in results if r["status"] == TaskStatus.COMPLETED.value])
    failed = len([r for r in results if r["status"] == TaskStatus.FAILED.value])

    print(f"\n=== 结果统计 ===")
    print(f"总任务数: {len(results)}")
    print(f"成功完成: {completed}")
    print(f"处理失败: {failed}")

    # 保存结果到文件
    with open("task_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"结果已保存到 task_results.json")
    print(f"[结果收集器] 结束")


def main():
    """
    主函数：协调多个生产者和消费者
    """
    print("=== 高级Queue通信示例：多生产者-多消费者模式 ===")

    # 配置参数
    num_producers = 2
    num_consumers = 3
    tasks_per_producer = 5
    total_tasks = num_producers * tasks_per_producer

    # 创建队列
    task_queue = multiprocessing.Queue(maxsize=10)
    result_queue = multiprocessing.Queue()

    processes = []

    try:
        # 创建并启动生产者进程
        print(f"\n启动 {num_producers} 个生产者进程...")
        for i in range(num_producers):
            producer = multiprocessing.Process(
                target=producer_process,
                args=(task_queue, result_queue, i + 1, tasks_per_producer),
            )
            producer.start()
            processes.append(producer)

        # 创建并启动消费者进程
        print(f"启动 {num_consumers} 个消费者进程...")
        for i in range(num_consumers):
            consumer = multiprocessing.Process(
                target=consumer_process, args=(task_queue, result_queue, i + 1)
            )
            consumer.start()
            processes.append(consumer)

        # 创建并启动结果收集进程
        print("启动结果收集进程...")
        collector = multiprocessing.Process(
            target=result_collector_process, args=(result_queue, total_tasks)
        )
        collector.start()
        processes.append(collector)

        # 等待所有生产者完成
        print("\n等待生产者完成...")
        for i, process in enumerate(processes[:num_producers]):
            process.join()
            print(f"生产者 {i+1} 已完成")

        # 向消费者发送停止信号
        print("\n向消费者发送停止信号...")
        for _ in range(num_consumers):
            task_queue.put({"message_type": MessageType.STOP.value})

        # 等待所有进程完成
        print("等待所有进程完成...")
        for process in processes:
            process.join()

    except KeyboardInterrupt:
        print("\n收到中断信号，正在终止所有进程...")
        for process in processes:
            process.terminate()
        for process in processes:
            process.join()

    print("\n=== 程序结束 ===")


if __name__ == "__main__":
    multiprocessing.freeze_support()
    main()
