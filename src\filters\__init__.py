"""
数字滤波器库

该库提供了一套完整的数字滤波器实现，包括：
- 低通滤波器 (Low-pass Filter)
- 高通滤波器 (High-pass Filter)  
- 带通滤波器 (Band-pass Filter)
- 带阻滤波器 (Band-stop Filter)
- 卡尔曼滤波器 (Kalman Filter)

所有滤波器都接受pandas Series作为输入，并返回滤波后的pandas Series。
"""

# 导入所有滤波器函数
from .low_pass import (
    low_pass_filter,
    moving_average_filter,
    exponential_smoothing_filter
)

from .high_pass import (
    high_pass_filter,
    difference_filter,
    detrend_filter
)

from .band_pass import (
    band_pass_filter,
    fft_band_pass_filter
)

from .band_stop import (
    band_stop_filter,
    notch_filter,
    fft_band_stop_filter
)

from .kalman import (
    kalman_filter,
    adaptive_kalman_filter,
    KalmanFilter
)

# 定义公开的API
__all__ = [
    # 低通滤波器
    'low_pass_filter',
    'moving_average_filter',
    'exponential_smoothing_filter',
    
    # 高通滤波器
    'high_pass_filter',
    'difference_filter',
    'detrend_filter',
    
    # 带通滤波器
    'band_pass_filter',
    'fft_band_pass_filter',
    
    # 带阻滤波器
    'band_stop_filter',
    'notch_filter',
    'fft_band_stop_filter',
    
    # 卡尔曼滤波器
    'kalman_filter',
    'adaptive_kalman_filter',
    'KalmanFilter'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'Augment Agent'
__description__ = '数字滤波器库 - 用于信号处理的完整滤波器集合'


def get_available_filters():
    """
    获取所有可用的滤波器列表
    
    返回:
        dict: 按类别组织的滤波器字典
    """
    return {
        '低通滤波器': [
            'low_pass_filter - 标准数字低通滤波器',
            'moving_average_filter - 移动平均滤波器',
            'exponential_smoothing_filter - 指数平滑滤波器'
        ],
        '高通滤波器': [
            'high_pass_filter - 标准数字高通滤波器',
            'difference_filter - 差分滤波器',
            'detrend_filter - 去趋势滤波器'
        ],
        '带通滤波器': [
            'band_pass_filter - 标准数字带通滤波器',
            'fft_band_pass_filter - 基于FFT的带通滤波器'
        ],
        '带阻滤波器': [
            'band_stop_filter - 标准数字带阻滤波器',
            'notch_filter - 陷波滤波器',
            'fft_band_stop_filter - 基于FFT的带阻滤波器'
        ],
        '卡尔曼滤波器': [
            'kalman_filter - 标准卡尔曼滤波器',
            'adaptive_kalman_filter - 自适应卡尔曼滤波器',
            'KalmanFilter - 卡尔曼滤波器类'
        ]
    }


def print_filter_info():
    """打印所有可用滤波器的信息"""
    filters = get_available_filters()
    
    print("=" * 60)
    print("数字滤波器库 - 可用滤波器")
    print("=" * 60)
    
    for category, filter_list in filters.items():
        print(f"\n{category}:")
        print("-" * 30)
        for filter_info in filter_list:
            print(f"  • {filter_info}")
    
    print("\n" + "=" * 60)
    print("使用示例:")
    print("from src.filters import low_pass_filter")
    print("filtered_data = low_pass_filter(data, cutoff_freq=10, sampling_freq=1000)")
    print("=" * 60)


# 如果直接运行此模块，显示滤波器信息
if __name__ == "__main__":
    print_filter_info()
