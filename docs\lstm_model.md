# LSTM模型文档

## 模型概述

本项目实现了基于PyTorch Lightning的LSTM（长短期记忆）模型，用于时间序列预测任务。该模型支持单步预测和多步预测两种模式，特别适用于钢带温度预测等工业场景中的时间序列数据。

## 模型架构

### 基础LSTM架构

模型的核心组件是LSTM（长短期记忆）网络，它由以下部分组成：

1. **LSTM层**：处理时序数据，捕获长期和短期依赖关系
2. **全连接层**：将LSTM的输出映射到目标预测值
3. **Dropout层**：在训练过程中随机关闭部分神经元，防止过拟合

### LitLSTM

`LitLSTM`类是基于PyTorch Lightning框架的LSTM实现，主要用于单步预测。它包含以下主要组件：

- LSTM模型
- 训练、验证和测试步骤的实现
- 优化器和学习率调度器的配置

### LitLSTMMultiStep

`LitLSTMMultiStep`类是`LitLSTM`的扩展版本，专门用于多步预测（预测未来多个时间步的值）。它通过重塑模型输出为多步预测格式，实现了对未来多个时间点的预测。

## 模型参数

### LSTM模型参数

| 参数          | 类型    | 默认值  | 描述                     |
|--------------|--------|---------|-------------------------|
| input_size   | int    | 必须指定 | 输入特征的维度            |
| hidden_size  | int    | 128     | LSTM隐藏层的维度         |
| num_layers   | int    | 2       | LSTM的层数              |
| output_size  | int    | 1       | 输出特征的维度           |
| dropout      | float  | 0.1     | Dropout比率            |
| bidirectional| bool   | False   | 是否使用双向LSTM         |

### 训练参数

| 参数          | 类型    | 默认值  | 描述                     |
|--------------|--------|---------|-------------------------|
| learning_rate| float  | 1e-3    | 学习率                   |
| weight_decay | float  | 1e-5    | 权重衰减系数             |
| epochs       | int    | 100     | 训练轮数                 |
| batch_size   | int    | 32      | 批次大小                 |
| patience     | int    | 10      | 早停耐心值               |
| gradient_clip_val | float | 1.0 | 梯度裁剪值               |

### 多步预测特有参数

| 参数          | 类型    | 默认值  | 描述                     |
|--------------|--------|---------|-------------------------|
| horizon      | int    | 1       | 预测的时间步数            |

## 使用方法

### 模型训练

可以使用`train_lstm.py`脚本训练模型，示例命令如下：

```bash
python -m src.model.train_lstm --start_time "2023-01-01" --end_time "2023-12-31" --hidden_size 256 --num_layers 3 --dropout 0.2 --epochs 50
```

#### 单步预测模式

```bash
python -m src.model.train_lstm --hidden_size 256 --num_layers 2 --learning_rate 0.001
```

#### 多步预测模式

```bash
python -m src.model.train_lstm --multi_step --horizon 5 --hidden_size 256 --num_layers 2
```

### 模型推理

以下是使用训练好的模型进行预测的示例代码：

```python
import torch
from src.model.lstm import LitLSTM, LitLSTMMultiStep

# 加载模型
model_path = "path/to/checkpoints/lstm_model.ckpt"
model = LitLSTM.load_from_checkpoint(model_path)

# 设置为评估模式
model.eval()

# 准备输入数据
# 输入形状: [批次大小, 序列长度, 特征数]
input_data = torch.randn(1, 255, 28)  # 示例数据

# 进行预测
with torch.no_grad():
    prediction = model(input_data)
    
print(prediction)
```

## 模型结构

### LSTM基础架构

LSTM网络能够学习长期依赖关系，其结构包含三个门控单元：

1. **遗忘门（Forget Gate）**：决定从细胞状态中丢弃哪些信息
2. **输入门（Input Gate）**：决定更新哪些信息
3. **输出门（Output Gate）**：决定输出哪些信息

### 单步预测流程

1. 输入序列通过LSTM层
2. 提取最后一个时间步的隐藏状态
3. 通过全连接层映射到预测值

### 多步预测流程

1. 输入序列通过LSTM层
2. 提取最后一个时间步的隐藏状态
3. 通过全连接层映射到多个预测值
4. 重塑输出为[batch_size, horizon, output_dim]格式

## 模型性能指标

模型评估使用以下指标：

- **MSE (均方误差)**：损失函数
- **MAE (平均绝对误差)**：评估预测值与实际值的平均绝对偏差
- **RMSE (均方根误差)**：MSE的平方根，常用于评估预测模型

## 优化策略

模型训练过程中使用以下优化策略：

1. **Adam优化器**：自适应学习率算法
2. **学习率调度**：使用ReduceLROnPlateau策略，在验证损失停止改善时降低学习率
3. **早停**：验证损失停止改善时提前结束训练
4. **梯度裁剪**：防止梯度爆炸
5. **Dropout**：防止过拟合

## 模型限制与最佳实践

### 限制

- LSTM模型对长序列的计算开销较大
- 可能需要较大的训练数据集以获得良好的泛化能力
- 对超参数较为敏感

### 最佳实践

- 根据任务复杂度调整隐藏层大小和层数
- 合理设置学习率和dropout率
- 使用早停和学习率调度防止过拟合
- 对输入数据进行合适的归一化处理
- 对于复杂任务，考虑增加模型复杂度或使用注意力机制
- 定期保存检查点，以便恢复最佳模型

## API参考

### LSTMModel

```python
LSTMModel(
    input_size: int,
    hidden_size: int,
    num_layers: int,
    output_size: int,
    dropout: float = 0.1,
    bidirectional: bool = False
)
```

基础LSTM模型实现。

### LitLSTM

```python
LitLSTM(
    input_size: int,
    hidden_size: int = 128,
    num_layers: int = 2,
    output_size: int = 1,
    dropout: float = 0.1,
    bidirectional: bool = False,
    learning_rate: float = 1e-3,
    weight_decay: float = 1e-5
)
```

PyTorch Lightning实现的LSTM模型，用于单步预测。

### LitLSTMMultiStep

```python
LitLSTMMultiStep(
    input_size: int,
    hidden_size: int = 128,
    num_layers: int = 2,
    output_size: int = 1,
    horizon: int = 1,
    dropout: float = 0.1,
    bidirectional: bool = False,
    learning_rate: float = 1e-3,
    weight_decay: float = 1e-5
)
```

PyTorch Lightning实现的多步预测LSTM模型。 