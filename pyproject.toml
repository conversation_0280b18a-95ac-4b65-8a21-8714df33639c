[project]
name = "deep-learning-for-annealing-furnace"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "influxdb>=5.3.2",
    "lightgbm>=4.6.0",
    "lightning>=2.5.1",
    "matplotlib>=3.10.1",
    "numpy>=2.2.4",
    "pandas>=2.2.3",
    "pandas-stubs>=2.3.0.250703",
    "pyswarm>=0.6",
    "scikit-learn>=1.6.1",
    "seaborn>=0.13.2",
    "tensorboard>=2.19.0",
    "torch>=2.6.0",
    "torchvision>=0.21.0",
]

[tool.uv.sources]
torch = [
  { index = "pytorch-cpu", marker = "sys_platform != 'win32'" },
  { index = "pytorch-cu124", marker = "sys_platform == 'win32'" },
]
torchvision = [
  { index = "pytorch-cpu", marker = "sys_platform != 'win32'" },
  { index = "pytorch-cu124", marker = "sys_platform == 'win32'" },
]

[[tool.uv.index]]
name = "pytorch-cpu"
url = "https://download.pytorch.org/whl/cpu"
explicit = true

[[tool.uv.index]]
name = "pytorch-cu124"
url = "https://download.pytorch.org/whl/cu124"
explicit = true
