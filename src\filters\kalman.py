"""
卡尔曼滤波器模块

该模块实现了卡尔曼滤波器，用于对含有噪声的动态系统进行状态估计。
卡尔曼滤波器是一种递归滤波器，能够从一系列包含噪声的测量中估计动态系统的状态。
"""

import numpy as np
import pandas as pd
from typing import Optional, Tuple, cast


class KalmanFilter:
    """
    一维卡尔曼滤波器类

    该类实现了标准的卡尔曼滤波算法，适用于一维信号的滤波处理。
    """

    def __init__(
        self,
        process_variance: float,
        measurement_variance: float,
        initial_state: Optional[float] = None,
        initial_covariance: Optional[float] = None,
    ):
        """
        初始化卡尔曼滤波器

        参数:
            process_variance (float): 过程噪声方差 (Q)
            measurement_variance (float): 测量噪声方差 (R)
            initial_state (float, optional): 初始状态估计，如果为None则使用第一个测量值
            initial_covariance (float, optional): 初始协方差估计，如果为None则使用测量噪声方差
        """
        self.process_variance = process_variance  # Q
        self.measurement_variance = measurement_variance  # R
        self.initial_state = initial_state
        self.initial_covariance = initial_covariance or measurement_variance

        # 状态变量
        self.state_estimate = None  # x
        self.error_covariance = None  # P

        # 系统模型参数（一维情况下都为1）
        self.state_transition = 1.0  # F
        self.observation_model = 1.0  # H

    def reset(self):
        """重置滤波器状态"""
        self.state_estimate = None
        self.error_covariance = None

    def predict(self) -> Tuple[Optional[float], Optional[float]]:
        """
        预测步骤

        返回:
            Tuple[Optional[float], Optional[float]]: (预测状态, 预测协方差)
        """
        if self.state_estimate is None or self.error_covariance is None:
            return None, None

        # 预测状态
        predicted_state = self.state_transition * self.state_estimate

        # 预测协方差
        predicted_covariance = (
            self.state_transition * self.error_covariance * self.state_transition
            + self.process_variance
        )

        return predicted_state, predicted_covariance

    def update(self, measurement: float) -> float:
        """
        更新步骤

        参数:
            measurement (float): 当前测量值

        返回:
            float: 更新后的状态估计
        """
        # 初始化
        if self.state_estimate is None:
            self.state_estimate = (
                self.initial_state if self.initial_state is not None else measurement
            )
            self.error_covariance = self.initial_covariance
            return self.state_estimate

        # 预测步骤
        predicted_state, predicted_covariance = self.predict()

        # 确保预测值不为None
        if predicted_state is None or predicted_covariance is None:
            return self.state_estimate

        # 计算卡尔曼增益
        innovation_covariance = (
            self.observation_model * predicted_covariance * self.observation_model
            + self.measurement_variance
        )
        kalman_gain = (
            predicted_covariance * self.observation_model / innovation_covariance
        )

        # 计算创新（测量残差）
        innovation = measurement - self.observation_model * predicted_state

        # 更新状态估计
        self.state_estimate = predicted_state + kalman_gain * innovation

        # 更新协方差
        self.error_covariance = (
            1 - kalman_gain * self.observation_model
        ) * predicted_covariance

        return self.state_estimate


def kalman_filter(
    data: pd.Series,
    process_variance: float,
    measurement_variance: float,
    initial_state: Optional[float] = None,
    initial_covariance: Optional[float] = None,
) -> pd.Series:
    """
    应用卡尔曼滤波器到输入信号

    参数:
        data (pd.Series): 输入信号数据（测量值序列）
        process_variance (float): 过程噪声方差，控制系统动态的不确定性
        measurement_variance (float): 测量噪声方差，控制测量的不确定性
        initial_state (float, optional): 初始状态估计，如果为None则使用第一个测量值
        initial_covariance (float, optional): 初始协方差估计，如果为None则使用测量噪声方差

    返回:
        pd.Series: 滤波后的信号数据

    异常:
        ValueError: 当参数不合法时抛出

    示例:
        >>> import pandas as pd
        >>> import numpy as np
        >>>
        >>> # 创建带噪声的测试信号
        >>> true_signal = np.sin(np.linspace(0, 4*np.pi, 100))
        >>> noisy_signal = true_signal + np.random.normal(0, 0.1, 100)
        >>> data = pd.Series(noisy_signal)
        >>>
        >>> # 应用卡尔曼滤波器
        >>> filtered_data = kalman_filter(data, process_variance=0.01, measurement_variance=0.1)
    """

    # 参数验证
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if process_variance <= 0:
        raise ValueError("过程噪声方差必须大于0")

    if measurement_variance <= 0:
        raise ValueError("测量噪声方差必须大于0")

    try:
        # 创建卡尔曼滤波器实例
        kf = KalmanFilter(
            process_variance=process_variance,
            measurement_variance=measurement_variance,
            initial_state=initial_state,
            initial_covariance=initial_covariance,
        )

        # 应用滤波器
        filtered_values = []
        for measurement in data.values:
            if np.isnan(measurement):
                # 处理NaN值：如果测量值为NaN，则跳过更新步骤，只进行预测
                if kf.state_estimate is not None:
                    predicted_state, _ = kf.predict()
                    filtered_values.append(predicted_state)
                else:
                    filtered_values.append(np.nan)
            else:
                filtered_state = kf.update(measurement)
                filtered_values.append(filtered_state)

        # 创建结果Series，保持原始索引
        result = pd.Series(filtered_values, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"卡尔曼滤波失败: {str(e)}")


def adaptive_kalman_filter(
    data: pd.Series,
    initial_process_variance: float = 0.01,
    initial_measurement_variance: float = 0.1,
    adaptation_rate: float = 0.01,
) -> pd.Series:
    """
    自适应卡尔曼滤波器

    该滤波器能够根据观测到的创新序列自动调整噪声参数。

    参数:
        data (pd.Series): 输入信号数据
        initial_process_variance (float): 初始过程噪声方差
        initial_measurement_variance (float): 初始测量噪声方差
        adaptation_rate (float): 自适应学习率

    返回:
        pd.Series: 滤波后的信号数据
    """
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if initial_process_variance <= 0:
        raise ValueError("初始过程噪声方差必须大于0")

    if initial_measurement_variance <= 0:
        raise ValueError("初始测量噪声方差必须大于0")

    if not 0 < adaptation_rate <= 1:
        raise ValueError("自适应学习率必须在(0, 1]范围内")

    try:
        # 初始化参数
        process_var = initial_process_variance
        measurement_var = initial_measurement_variance

        # 创建卡尔曼滤波器
        kf = KalmanFilter(process_var, measurement_var)

        filtered_values = []
        innovations = []

        for measurement in data.values:
            if np.isnan(measurement):
                if kf.state_estimate is not None:
                    predicted_state, _ = kf.predict()
                    filtered_values.append(predicted_state)
                else:
                    filtered_values.append(np.nan)
                continue

            # 记录更新前的状态用于计算创新
            if kf.state_estimate is not None:
                predicted_state, _ = kf.predict()
                innovation = measurement - predicted_state
                innovations.append(innovation)

            # 更新滤波器
            filtered_state = kf.update(measurement)
            filtered_values.append(filtered_state)

            # 自适应调整参数（在有足够历史数据后）
            if len(innovations) > 10:
                recent_innovations = innovations[-10:]
                innovation_variance = np.var(recent_innovations)

                # 调整测量噪声方差
                measurement_var = (
                    1 - adaptation_rate
                ) * measurement_var + adaptation_rate * innovation_variance
                kf.measurement_variance = float(measurement_var)

        # 创建结果Series
        result = pd.Series(filtered_values, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"自适应卡尔曼滤波失败: {str(e)}")
