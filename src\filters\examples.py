"""
数字滤波器使用示例

该文件展示了如何使用filters库中的各种滤波器。
包含了每种滤波器的基本使用方法和参数设置建议。
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Optional

# 导入滤波器函数
from . import (
    low_pass_filter,
    high_pass_filter,
    band_pass_filter,
    band_stop_filter,
    kalman_filter,
    moving_average_filter,
    notch_filter
)


def create_test_signal(
    duration: float = 1.0,
    sampling_freq: float = 1000.0,
    frequencies: list = [5, 25, 50],
    amplitudes: list = [1.0, 0.5, 0.3],
    noise_level: float = 0.1
) -> pd.Series:
    """
    创建测试信号
    
    参数:
        duration: 信号持续时间（秒）
        sampling_freq: 采样频率（Hz）
        frequencies: 信号频率列表（Hz）
        amplitudes: 对应的幅度列表
        noise_level: 噪声水平
    
    返回:
        pd.Series: 测试信号
    """
    t = np.linspace(0, duration, int(duration * sampling_freq))
    signal = np.zeros_like(t)
    
    # 添加多个频率成分
    for freq, amp in zip(frequencies, amplitudes):
        signal += amp * np.sin(2 * np.pi * freq * t)
    
    # 添加噪声
    if noise_level > 0:
        signal += np.random.normal(0, noise_level, len(signal))
    
    return pd.Series(signal, index=t, name='test_signal')


def example_low_pass_filter():
    """低通滤波器示例"""
    print("=" * 50)
    print("低通滤波器示例")
    print("=" * 50)
    
    # 创建测试信号：包含5Hz、25Hz、50Hz的成分
    signal = create_test_signal(frequencies=[5, 25, 50], amplitudes=[1.0, 0.5, 0.3])
    
    # 应用低通滤波器，截止频率为15Hz，保留5Hz成分
    filtered = low_pass_filter(
        data=signal,
        cutoff_freq=15.0,
        sampling_freq=1000.0,
        order=4,
        filter_type='butterworth'
    )
    
    print(f"原始信号长度: {len(signal)}")
    print(f"滤波后信号长度: {len(filtered)}")
    print(f"滤波前信号标准差: {signal.std():.4f}")
    print(f"滤波后信号标准差: {filtered.std():.4f}")
    
    # 移动平均滤波器示例
    ma_filtered = moving_average_filter(signal, window_size=20)
    print(f"移动平均滤波后标准差: {ma_filtered.std():.4f}")
    
    return signal, filtered, ma_filtered


def example_high_pass_filter():
    """高通滤波器示例"""
    print("\n" + "=" * 50)
    print("高通滤波器示例")
    print("=" * 50)
    
    # 创建包含低频趋势的信号
    t = np.linspace(0, 1, 1000)
    trend = 0.5 * t  # 线性趋势
    signal_data = np.sin(2 * np.pi * 50 * t) + trend + np.random.normal(0, 0.05, 1000)
    signal = pd.Series(signal_data, index=t, name='signal_with_trend')
    
    # 应用高通滤波器，去除低频成分
    filtered = high_pass_filter(
        data=signal,
        cutoff_freq=20.0,
        sampling_freq=1000.0,
        order=4
    )
    
    print(f"原始信号均值: {signal.mean():.4f}")
    print(f"滤波后信号均值: {filtered.mean():.4f}")
    print(f"趋势去除效果: {abs(filtered.mean()) < 0.01}")
    
    return signal, filtered


def example_band_pass_filter():
    """带通滤波器示例"""
    print("\n" + "=" * 50)
    print("带通滤波器示例")
    print("=" * 50)
    
    # 创建包含多个频率成分的信号
    signal = create_test_signal(frequencies=[5, 25, 50, 100], amplitudes=[1.0, 1.0, 1.0, 0.5])
    
    # 应用带通滤波器，只保留20-30Hz的成分
    filtered = band_pass_filter(
        data=signal,
        low_cutoff=20.0,
        high_cutoff=30.0,
        sampling_freq=1000.0,
        order=4
    )
    
    print(f"原始信号功率: {(signal**2).mean():.4f}")
    print(f"滤波后信号功率: {(filtered**2).mean():.4f}")
    print(f"功率保留比例: {(filtered**2).mean() / (signal**2).mean():.2%}")
    
    return signal, filtered


def example_band_stop_filter():
    """带阻滤波器示例"""
    print("\n" + "=" * 50)
    print("带阻滤波器示例")
    print("=" * 50)
    
    # 创建信号，其中包含50Hz的工频干扰
    signal = create_test_signal(frequencies=[10, 50, 80], amplitudes=[1.0, 2.0, 0.5])
    
    # 使用陷波滤波器去除50Hz干扰
    filtered = notch_filter(
        data=signal,
        notch_freq=50.0,
        sampling_freq=1000.0,
        quality_factor=30.0
    )
    
    # 计算50Hz成分的功率衰减
    fft_orig = np.fft.fft(signal.values)
    fft_filt = np.fft.fft(filtered.values)
    freqs = np.fft.fftfreq(len(signal), 1/1000.0)
    
    # 找到50Hz附近的频率索引
    idx_50hz = np.argmin(np.abs(freqs - 50))
    power_reduction = abs(fft_filt[idx_50hz]) / abs(fft_orig[idx_50hz])
    
    print(f"50Hz成分功率衰减: {(1-power_reduction)*100:.1f}%")
    print(f"滤波效果: {'良好' if power_reduction < 0.1 else '一般'}")
    
    return signal, filtered


def example_kalman_filter():
    """卡尔曼滤波器示例"""
    print("\n" + "=" * 50)
    print("卡尔曼滤波器示例")
    print("=" * 50)
    
    # 创建带噪声的平滑信号
    t = np.linspace(0, 4*np.pi, 200)
    true_signal = np.sin(t)
    noisy_signal = true_signal + np.random.normal(0, 0.3, len(t))
    signal = pd.Series(noisy_signal, index=t, name='noisy_signal')
    
    # 应用卡尔曼滤波器
    filtered = kalman_filter(
        data=signal,
        process_variance=0.01,  # 过程噪声较小，信号变化平滑
        measurement_variance=0.09  # 测量噪声方差
    )
    
    # 计算滤波效果
    mse_original = np.mean((noisy_signal - true_signal)**2)
    mse_filtered = np.mean((filtered.values - true_signal)**2)
    improvement = (mse_original - mse_filtered) / mse_original * 100
    
    print(f"原始信号MSE: {mse_original:.4f}")
    print(f"滤波后MSE: {mse_filtered:.4f}")
    print(f"改善程度: {improvement:.1f}%")
    
    return signal, filtered, pd.Series(true_signal, index=t, name='true_signal')


def run_all_examples():
    """运行所有示例"""
    print("数字滤波器库使用示例")
    print("=" * 60)
    
    # 运行各种滤波器示例
    signal1, lp_filtered, ma_filtered = example_low_pass_filter()
    signal2, hp_filtered = example_high_pass_filter()
    signal3, bp_filtered = example_band_pass_filter()
    signal4, bs_filtered = example_band_stop_filter()
    signal5, kf_filtered, true_signal = example_kalman_filter()
    
    print("\n" + "=" * 60)
    print("所有示例运行完成！")
    print("建议：结合matplotlib可视化滤波效果")
    print("=" * 60)
    
    return {
        'low_pass': (signal1, lp_filtered, ma_filtered),
        'high_pass': (signal2, hp_filtered),
        'band_pass': (signal3, bp_filtered),
        'band_stop': (signal4, bs_filtered),
        'kalman': (signal5, kf_filtered, true_signal)
    }


def plot_filter_comparison(results: dict, save_plots: bool = False):
    """
    绘制滤波器效果对比图
    
    参数:
        results: run_all_examples()的返回结果
        save_plots: 是否保存图片
    """
    try:
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('数字滤波器效果对比', fontsize=16)
        
        # 低通滤波器
        ax = axes[0, 0]
        signal, filtered, ma_filtered = results['low_pass']
        ax.plot(signal.index[:200], signal.values[:200], 'b-', alpha=0.7, label='原始信号')
        ax.plot(filtered.index[:200], filtered.values[:200], 'r-', label='低通滤波')
        ax.set_title('低通滤波器')
        ax.legend()
        ax.grid(True)
        
        # 高通滤波器
        ax = axes[0, 1]
        signal, filtered = results['high_pass']
        ax.plot(signal.index[:200], signal.values[:200], 'b-', alpha=0.7, label='原始信号')
        ax.plot(filtered.index[:200], filtered.values[:200], 'r-', label='高通滤波')
        ax.set_title('高通滤波器')
        ax.legend()
        ax.grid(True)
        
        # 带通滤波器
        ax = axes[0, 2]
        signal, filtered = results['band_pass']
        ax.plot(signal.index[:200], signal.values[:200], 'b-', alpha=0.7, label='原始信号')
        ax.plot(filtered.index[:200], filtered.values[:200], 'r-', label='带通滤波')
        ax.set_title('带通滤波器')
        ax.legend()
        ax.grid(True)
        
        # 带阻滤波器
        ax = axes[1, 0]
        signal, filtered = results['band_stop']
        ax.plot(signal.index[:200], signal.values[:200], 'b-', alpha=0.7, label='原始信号')
        ax.plot(filtered.index[:200], filtered.values[:200], 'r-', label='带阻滤波')
        ax.set_title('带阻滤波器')
        ax.legend()
        ax.grid(True)
        
        # 卡尔曼滤波器
        ax = axes[1, 1]
        signal, filtered, true_signal = results['kalman']
        ax.plot(signal.index[:100], signal.values[:100], 'b-', alpha=0.5, label='噪声信号')
        ax.plot(filtered.index[:100], filtered.values[:100], 'r-', label='卡尔曼滤波')
        ax.plot(true_signal.index[:100], true_signal.values[:100], 'g--', label='真实信号')
        ax.set_title('卡尔曼滤波器')
        ax.legend()
        ax.grid(True)
        
        # 频谱对比
        ax = axes[1, 2]
        signal = results['low_pass'][0]
        filtered = results['low_pass'][1]
        
        # 计算频谱
        freqs = np.fft.fftfreq(len(signal), 1/1000.0)[:len(signal)//2]
        fft_orig = np.abs(np.fft.fft(signal.values))[:len(signal)//2]
        fft_filt = np.abs(np.fft.fft(filtered.values))[:len(signal)//2]
        
        ax.semilogy(freqs, fft_orig, 'b-', alpha=0.7, label='原始频谱')
        ax.semilogy(freqs, fft_filt, 'r-', label='滤波后频谱')
        ax.set_title('频谱对比')
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('幅度')
        ax.legend()
        ax.grid(True)
        ax.set_xlim(0, 100)
        
        plt.tight_layout()
        
        if save_plots:
            plt.savefig('filter_comparison.png', dpi=300, bbox_inches='tight')
            print("图片已保存为 filter_comparison.png")
        
        plt.show()
        
    except ImportError:
        print("matplotlib未安装，无法绘制图形")
        print("请安装matplotlib: pip install matplotlib")


if __name__ == "__main__":
    # 运行所有示例
    results = run_all_examples()
    
    # 尝试绘制对比图
    plot_filter_comparison(results)
