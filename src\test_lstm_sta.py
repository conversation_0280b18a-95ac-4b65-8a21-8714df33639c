import logging
import os

import lightning as L

from datetime import datetime
from config import Configs, Logger
from data_loader import AFLoader
from model.lstm_sta import LitLSTMTA
from lightning.pytorch.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
)
from lightning.pytorch.loggers import TensorBoardLogger


def test_af_loader():
    Configs.initialize()
    Logger.initialize()

    train_config = Configs.get_train_config()
    model_config = Configs.get_lstm_sta_config()

    start_time = datetime.strptime("2025-05-01", "%Y-%m-%d")
    end_time = datetime.strptime("2025-06-30", "%Y-%m-%d")

    # work_dir = f"data/test/{datetime.now().strftime("%Y%m%d")}"
    work_dir = f"data/test-lstm-sta/"
    data_loader = AFLoader(start_time=start_time, end_time=end_time, work_dir=work_dir)
    # data_loader.prepare_data()
    data_loader.setup()

    model = LitLSTMTA(
        input_size=train_config.input_len * model_config.lstm_input_size,
        lstm_input_size=model_config.lstm_input_size,
        lstm_hidden_size=model_config.lstm_hidden_size,
        output_size=len(train_config.target_columns),
        sequence_length=train_config.input_len,
        dropout=model_config.dropout,
        learning_rate=model_config.learning_rate,
        weight_decay=model_config.weight_decay,
    )

    # 设置回调
    checkpoint_callback = ModelCheckpoint(
        monitor="val_loss",
        filename="lstmsta-{epoch:02d}-{val_loss:.4f}",
        save_top_k=3,
        mode="min",
    )

    early_stopping = EarlyStopping(
        monitor="val_loss",
        patience=train_config.early_stopping_patience,
        mode="min",
    )

    lr_monitor = LearningRateMonitor(logging_interval="epoch")

    # 设置日志记录器
    tensorboard_logger = TensorBoardLogger("logs", name="lstm-sta")

    # 创建训练器
    trainer = L.Trainer(
        max_epochs=train_config.epochs,
        callbacks=[checkpoint_callback, early_stopping, lr_monitor],
        logger=tensorboard_logger,
        accelerator=train_config.accelerator,
        devices=train_config.devices,
        gradient_clip_val=train_config.gradient_clip_val,
    )

    # 训练模型
    trainer.fit(
        model=model,
        train_dataloaders=data_loader.train_dataloader(),
        val_dataloaders=data_loader.val_dataloader(),
    )

    # 评估模型
    trainer.test(model=model, dataloaders=data_loader.test_dataloader())

    # 保存模型
    save_path = os.path.join(work_dir, "lstm_sta_model.ckpt")
    trainer.save_checkpoint(save_path)
    print(f"模型已保存到 {save_path}")


if __name__ == "__main__":
    test_af_loader()
