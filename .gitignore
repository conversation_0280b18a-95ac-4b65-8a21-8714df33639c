# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.env/
.venv/
pythonenv*/

# PyTorch
*.pt
*.pth
*.ckpt
*.model
.pyTorch/
runs/

# 数据文件（根据需要调整）
data/
datasets/
raw_data/
processed_data/
*.csv
*.xlsx
*.xls
*.json
*.pkl
*.h5
*.hdf5
*.parquet
*.feather

# 日志和输出
logs/
tensorboard/
outputs/
results/
*.log
wandb/

# Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*
*.ipynb_meta
profile_default/
ipython_config.py

# 编辑器和IDE
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
*.swp
*.swo
*~
.vs/

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
tmp/
temp/
.temp/
.tmp/
*.bak
*.tmp

# 绘图输出
*.png
*.jpg
*.jpeg
*.gif
*.pdf
figures/
plots/

# 配置文件（根据需要调整）
# config.yaml
# settings.json

# 本地开发文件
.env.local
.env.development.local
.env.test.local
.env.production.local
.cursor/rules/*.mdc

MNIST/*
.env
