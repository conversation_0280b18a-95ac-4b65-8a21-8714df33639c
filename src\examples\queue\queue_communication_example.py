import multiprocessing
import time
import random
from datetime import datetime


def sender_process(queue, process_name):
    """
    发送进程：定期向队列发送消息
    """
    print(f"[{process_name}] 发送进程启动")

    for i in range(10):
        # 创建要发送的消息
        message = {
            "id": i + 1,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": f"来自{process_name}的消息 #{i + 1}",
            "value": random.randint(1, 100),
        }

        # 发送消息到队列
        queue.put(message)
        print(f"[{process_name}] 发送消息: {message['data']}, 值: {message['value']}")

        # 随机等待1-3秒
        time.sleep(random.uniform(1, 3))

    # 发送结束信号
    queue.put(None)
    print(f"[{process_name}] 发送进程结束")


def receiver_process(queue, process_name):
    """
    接收进程：从队列接收消息并处理
    """
    print(f"[{process_name}] 接收进程启动")

    processed_count = 0

    while True:
        try:
            # 从队列获取消息（阻塞等待）
            message = queue.get(timeout=10)  # 10秒超时

            # 检查是否为结束信号
            if message is None:
                print(f"[{process_name}] 收到结束信号，停止接收")
                break

            # 处理消息
            print(f"[{process_name}] 接收到消息: {message['data']}")
            print(
                f"[{process_name}] 消息详情: ID={message['id']}, 时间={message['timestamp']}, 值={message['value']}"
            )

            # 模拟处理时间
            processing_time = random.uniform(0.5, 2.0)
            time.sleep(processing_time)

            # 处理逻辑示例：对值进行计算
            processed_value = message["value"] * 2
            processed_count += 1

            print(
                f"[{process_name}] 处理完成: 原值={message['value']}, 处理后={processed_value}, 处理时间={processing_time:.2f}秒"
            )
            print(f"[{process_name}] 已处理消息总数: {processed_count}")
            print("-" * 50)

        except multiprocessing.TimeoutError:
            print(f"[{process_name}] 等待消息超时，继续等待...")
        except Exception as e:
            print(f"[{process_name}] 处理消息时出错: {e}")
            break

    print(f"[{process_name}] 接收进程结束，总共处理了 {processed_count} 条消息")


def main():
    """
    主函数：创建队列和进程
    """
    print("=== 进程间Queue通信示例 ===")

    # 创建队列
    message_queue = multiprocessing.Queue(maxsize=5)  # 最大容量为5

    # 创建进程
    sender = multiprocessing.Process(
        target=sender_process, args=(message_queue, "发送者")
    )

    receiver = multiprocessing.Process(
        target=receiver_process, args=(message_queue, "接收者")
    )

    try:
        # 启动进程
        print("启动发送进程...")
        sender.start()

        print("启动接收进程...")
        receiver.start()

        # 等待发送进程完成
        sender.join()
        print("发送进程已完成")

        # 等待接收进程完成
        receiver.join()
        print("接收进程已完成")

    except KeyboardInterrupt:
        print("\n收到中断信号，正在终止进程...")
        sender.terminate()
        receiver.terminate()
        sender.join()
        receiver.join()

    print("=== 程序结束 ===")


if __name__ == "__main__":
    # 在Windows上需要这个保护
    multiprocessing.freeze_support()
    main()
