# 注意力增强LSTM模型文档

## 概述

本文档介绍了基于现有 `LitLSTM` 模型实现的注意力增强LSTM模型。新模型在原有LSTM架构的基础上集成了注意力机制，能够更好地捕获时间序列中的长期依赖关系和重要特征。

## 模型架构

### 1. 核心组件

#### 1.1 注意力层
- **SelfAttention**: 自注意力机制，计算序列内部元素之间的注意力权重
- **MultiHeadAttention**: 多头注意力机制，并行计算多个注意力头，捕获不同类型的依赖关系

#### 1.2 LSTM + 注意力模型
- **LSTMWithAttentionModel**: 基础模型类，集成LSTM和注意力机制
- **LitLSTMWithAttention**: PyTorch Lightning包装的单步预测模型
- **LitLSTMWithAttentionMultiStep**: PyTorch Lightning包装的多步预测模型

### 2. 模型流程

```
输入序列 → LSTM层 → 注意力层 → 层归一化 → 全局池化 → 全连接层 → 输出
```

1. **LSTM处理**: 输入序列通过LSTM层，提取时序特征
2. **注意力计算**: 对LSTM输出应用注意力机制，突出重要时间步
3. **残差连接**: 可选的残差连接，防止梯度消失
4. **层归一化**: 稳定训练过程
5. **全局池化**: 使用注意力加权的全局平均池化
6. **输出预测**: 通过全连接层生成最终预测

## 使用方法

### 1. 基本使用

```python
from src.model.lstm import LitLSTMWithAttention

# 创建自注意力模型
model = LitLSTMWithAttention(
    input_size=10,
    hidden_size=128,
    num_layers=2,
    output_size=1,
    attention_type="self",
    dropout=0.1,
    learning_rate=1e-3
)

# 创建多头注意力模型
model = LitLSTMWithAttention(
    input_size=10,
    hidden_size=128,
    num_layers=2,
    output_size=1,
    attention_type="multi_head",
    num_attention_heads=8,
    use_attention_residual=True,
    dropout=0.1,
    learning_rate=1e-3
)
```

### 2. 多步预测

```python
from src.model.lstm import LitLSTMWithAttentionMultiStep

# 创建多步预测模型
model = LitLSTMWithAttentionMultiStep(
    input_size=10,
    hidden_size=128,
    num_layers=2,
    output_size=1,
    horizon=5,  # 预测未来5个时间步
    attention_type="self",
    dropout=0.1,
    learning_rate=1e-3
)
```

### 3. 训练脚本使用

```bash
# 使用自注意力机制训练
python src/model/train_lstm.py \
    --use_attention \
    --attention_type self \
    --hidden_size 128 \
    --num_layers 2 \
    --epochs 50

# 使用多头注意力机制训练
python src/model/train_lstm.py \
    --use_attention \
    --attention_type multi_head \
    --num_attention_heads 8 \
    --use_attention_residual \
    --hidden_size 128 \
    --num_layers 2 \
    --epochs 50
```

## 参数说明

### 模型参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `input_size` | int | - | 输入特征维度 |
| `hidden_size` | int | 128 | LSTM隐藏层维度 |
| `num_layers` | int | 2 | LSTM层数 |
| `output_size` | int | 1 | 输出特征维度 |
| `dropout` | float | 0.1 | Dropout比率 |
| `bidirectional` | bool | False | 是否使用双向LSTM |
| `attention_type` | str | "self" | 注意力类型："self"或"multi_head" |
| `num_attention_heads` | int | 8 | 多头注意力的头数 |
| `use_attention_residual` | bool | True | 是否使用残差连接 |
| `learning_rate` | float | 1e-3 | 学习率 |
| `weight_decay` | float | 1e-5 | 权重衰减系数 |

### 多步预测额外参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `horizon` | int | 1 | 预测的时间步数 |

## 性能对比

基于合成数据的测试结果：

| 模型 | 测试损失 | MAE | RMSE | 参数数量 |
|------|----------|-----|------|----------|
| 原始LSTM | 3.0732 | 1.3962 | 1.7383 | 53,569 |
| 自注意力LSTM | 3.5302 | 1.4518 | 1.8252 | 66,177 |
| 多头注意力LSTM | 4.9122 | 1.7301 | 2.1112 | 70,337 |

**注意**: 性能表现取决于具体的数据集和任务。在某些复杂的时间序列任务中，注意力机制可能会显著提升性能。

## 优势与特点

### 优势
1. **长期依赖**: 注意力机制能够直接建模长距离依赖关系
2. **特征突出**: 自动学习重要时间步的权重
3. **灵活性**: 支持自注意力和多头注意力两种模式
4. **兼容性**: 与现有数据处理流程完全兼容
5. **可扩展**: 支持单步和多步预测

### 特点
1. **残差连接**: 防止梯度消失，稳定训练
2. **层归一化**: 加速收敛，提高稳定性
3. **全局池化**: 注意力加权的全局平均池化
4. **参数可配置**: 灵活的超参数设置

## 使用建议

### 1. 选择注意力类型
- **自注意力**: 适合中等复杂度的时间序列，参数较少
- **多头注意力**: 适合复杂的时间序列模式，能学习多种依赖关系

### 2. 超参数调优
- **注意力头数**: 通常设置为4-16，过多可能导致过拟合
- **隐藏层大小**: 建议与LSTM隐藏层大小相同或相近
- **残差连接**: 对于深层网络建议开启

### 3. 训练策略
- **学习率**: 注意力模型可能需要较小的学习率
- **正则化**: 增加dropout和权重衰减防止过拟合
- **早停**: 监控验证损失，避免过拟合

### 4. 数据要求
- **序列长度**: 注意力机制在较长序列上效果更明显
- **数据质量**: 确保数据预处理质量，注意力机制对噪声敏感
- **特征工程**: 良好的特征工程能提升注意力机制效果

## 示例代码

完整的使用示例请参考：
- `examples/attention_lstm_usage.py`: 完整的使用示例和性能对比
- `src/model/test_attention_lstm.py`: 单元测试和功能验证

## 注意事项

1. **计算复杂度**: 注意力机制会增加计算开销，特别是多头注意力
2. **内存使用**: 注意力权重矩阵会增加内存使用
3. **训练时间**: 相比原始LSTM，训练时间会有所增加
4. **超参数敏感**: 注意力机制对超参数设置较为敏感
5. **数据依赖**: 在小数据集上可能不如简单模型

## 扩展方向

1. **位置编码**: 添加位置编码增强时序信息
2. **交叉注意力**: 实现多变量之间的交叉注意力
3. **稀疏注意力**: 对于超长序列实现稀疏注意力机制
4. **可解释性**: 添加注意力权重可视化功能
