# LSTM模型文档

## 概述

本目录包含了基于PyTorch Lightning实现的LSTM（长短期记忆）模型的详细文档。该模型专为工业时间序列预测任务设计，特别适用于钢带温度预测等应用场景。

## 文档结构

- **[lstm_model.md](lstm_model.md)**: LSTM模型的详细说明，包括模型参数、API和使用方法
- **[lstm_architecture.md](lstm_architecture.md)**: LSTM模型架构的详细解析，包括数据流和实现细节
- **[lstm_example.md](lstm_example.md)**: 使用LSTM模型进行预测的代码示例

## 模型特点

- 基于PyTorch Lightning框架，简化训练和验证流程
- 支持单步预测和多步预测两种模式
- 可配置的隐藏层大小、层数和双向性
- 集成了学习率调度、早停和梯度裁剪等优化策略
- 适用于工业时间序列数据的特点，如非平稳性和多变量输入

## 快速入门

### 模型训练

```bash
# 单步预测模式
python -m src.model.train_lstm --hidden_size 256 --num_layers 2 --learning_rate 0.001

# 多步预测模式
python -m src.model.train_lstm --multi_step --horizon 5 --hidden_size 256 --num_layers 2
```

### 模型推理

```python
import torch
from src.model.lstm import LitLSTM

# 加载模型
model_path = "path/to/checkpoints/lstm_model.ckpt"
model = LitLSTM.load_from_checkpoint(model_path)
model.eval()

# 准备输入数据
input_data = torch.randn(1, 255, 28)  # [batch_size, seq_len, features]

# 进行预测
with torch.no_grad():
    prediction = model(input_data)
    
print(prediction)
```

## 进一步阅读

- 详细的模型架构和实现细节请参考 [lstm_architecture.md](lstm_architecture.md)
- 完整的API文档和参数说明请参考 [lstm_model.md](lstm_model.md)
- 更多使用示例请参考 [lstm_example.md](lstm_example.md) 