"""
带阻滤波器模块

该模块实现了数字带阻滤波器（也称为陷波滤波器），用于去除信号中特定频率范围的成分，保留其他频率成分。
支持多种滤波器类型：Butterworth、Chebyshev I、Chebyshev II、Elliptic等。
"""

import numpy as np
import pandas as pd
from scipy import signal
from typing import Optional


def band_stop_filter(
    data: pd.Series,
    low_cutoff: float,
    high_cutoff: float,
    sampling_freq: float,
    order: int = 4,
    filter_type: str = "butterworth",
    rp: Optional[float] = None,
    rs: Optional[float] = None,
) -> pd.Series:
    """
    应用带阻滤波器到输入信号

    参数:
        data (pd.Series): 输入信号数据
        low_cutoff (float): 低截止频率 (Hz)
        high_cutoff (float): 高截止频率 (Hz)
        sampling_freq (float): 采样频率 (Hz)
        order (int): 滤波器阶数，默认为4
        filter_type (str): 滤波器类型，可选：
            - 'butterworth': Butterworth滤波器（默认）
            - 'chebyshev1': Chebyshev I型滤波器
            - 'chebyshev2': Chebyshev II型滤波器
            - 'elliptic': 椭圆滤波器
        rp (float, optional): 通带最大衰减 (dB)，用于Chebyshev I和椭圆滤波器
        rs (float, optional): 阻带最小衰减 (dB)，用于Chebyshev II和椭圆滤波器

    返回:
        pd.Series: 滤波后的信号数据

    异常:
        ValueError: 当参数不合法时抛出

    示例:
        >>> import pandas as pd
        >>> import numpy as np
        >>>
        >>> # 创建测试信号
        >>> t = np.linspace(0, 1, 1000)
        >>> signal_data = np.sin(2 * np.pi * 5 * t) + np.sin(2 * np.pi * 25 * t) + np.sin(2 * np.pi * 50 * t)
        >>> data = pd.Series(signal_data)
        >>>
        >>> # 应用带阻滤波器，去除20-30Hz的信号
        >>> filtered_data = band_stop_filter(data, low_cutoff=20, high_cutoff=30, sampling_freq=1000)
    """

    # 参数验证
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if low_cutoff <= 0:
        raise ValueError("低截止频率必须大于0")

    if high_cutoff <= 0:
        raise ValueError("高截止频率必须大于0")

    if low_cutoff >= high_cutoff:
        raise ValueError("低截止频率必须小于高截止频率")

    if sampling_freq <= 0:
        raise ValueError("采样频率必须大于0")

    if high_cutoff >= sampling_freq / 2:
        raise ValueError("高截止频率必须小于奈奎斯特频率 (采样频率的一半)")

    if order <= 0:
        raise ValueError("滤波器阶数必须大于0")

    # 计算归一化截止频率
    nyquist_freq = sampling_freq / 2
    normalized_low = low_cutoff / nyquist_freq
    normalized_high = high_cutoff / nyquist_freq

    try:
        # 根据滤波器类型设计滤波器
        if filter_type.lower() == "butterworth":
            b, a = signal.butter(  # type: ignore
                order, [normalized_low, normalized_high], btype="bandstop", analog=False
            )  # type: ignore

        elif filter_type.lower() == "chebyshev1":
            if rp is None:
                rp = 1.0  # 默认通带衰减1dB
            b, a = signal.cheby1(  # type: ignore
                order,
                rp,
                [normalized_low, normalized_high],
                btype="bandstop",
                analog=False,
            )  # type: ignore

        elif filter_type.lower() == "chebyshev2":
            if rs is None:
                rs = 40.0  # 默认阻带衰减40dB
            b, a = signal.cheby2(  # type: ignore
                order,
                rs,
                [normalized_low, normalized_high],
                btype="bandstop",
                analog=False,
            )  # type: ignore

        elif filter_type.lower() == "elliptic":
            if rp is None:
                rp = 1.0  # 默认通带衰减1dB
            if rs is None:
                rs = 40.0  # 默认阻带衰减40dB
            b, a = signal.ellip(  # type: ignore
                order,
                rp,
                rs,
                [normalized_low, normalized_high],
                btype="bandstop",
                analog=False,
            )  # type: ignore

        else:
            raise ValueError(f"不支持的滤波器类型: {filter_type}")

        # 应用滤波器
        filtered_data = signal.filtfilt(b, a, data.values)

        # 创建结果Series，保持原始索引
        result = pd.Series(filtered_data, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"滤波器设计或应用失败: {str(e)}")


def notch_filter(
    data: pd.Series,
    notch_freq: float,
    sampling_freq: float,
    quality_factor: float = 30.0,
) -> pd.Series:
    """
    陷波滤波器（特定频率的带阻滤波器）

    参数:
        data (pd.Series): 输入信号数据
        notch_freq (float): 陷波频率 (Hz)
        sampling_freq (float): 采样频率 (Hz)
        quality_factor (float): 品质因子，控制陷波的宽度，默认为30.0

    返回:
        pd.Series: 滤波后的信号数据
    """
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if notch_freq <= 0:
        raise ValueError("陷波频率必须大于0")

    if sampling_freq <= 0:
        raise ValueError("采样频率必须大于0")

    if notch_freq >= sampling_freq / 2:
        raise ValueError("陷波频率必须小于奈奎斯特频率 (采样频率的一半)")

    if quality_factor <= 0:
        raise ValueError("品质因子必须大于0")

    try:
        # 设计陷波滤波器
        b, a = signal.iirnotch(notch_freq, quality_factor, sampling_freq)  # type: ignore

        # 应用滤波器
        filtered_data = signal.filtfilt(b, a, data.values)

        # 创建结果Series，保持原始索引
        result = pd.Series(filtered_data, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"陷波滤波器设计或应用失败: {str(e)}")


def fft_band_stop_filter(
    data: pd.Series, low_cutoff: float, high_cutoff: float, sampling_freq: float
) -> pd.Series:
    """
    基于FFT的带阻滤波器（频域滤波）

    参数:
        data (pd.Series): 输入信号数据
        low_cutoff (float): 低截止频率 (Hz)
        high_cutoff (float): 高截止频率 (Hz)
        sampling_freq (float): 采样频率 (Hz)

    返回:
        pd.Series: 滤波后的信号数据
    """
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if low_cutoff <= 0:
        raise ValueError("低截止频率必须大于0")

    if high_cutoff <= 0:
        raise ValueError("高截止频率必须大于0")

    if low_cutoff >= high_cutoff:
        raise ValueError("低截止频率必须小于高截止频率")

    if sampling_freq <= 0:
        raise ValueError("采样频率必须大于0")

    try:
        # 计算FFT
        fft_data = np.fft.fft(data.values)  # type: ignore
        freqs = np.fft.fftfreq(len(data), 1 / sampling_freq)

        # 创建频域滤波器（带阻）
        filter_mask = np.ones_like(freqs, dtype=bool)
        filter_mask[(np.abs(freqs) >= low_cutoff) & (np.abs(freqs) <= high_cutoff)] = (
            False
        )

        # 应用滤波器
        filtered_fft = fft_data * filter_mask

        # 逆FFT
        filtered_data = np.real(np.fft.ifft(filtered_fft))

        # 创建结果Series，保持原始索引
        result = pd.Series(filtered_data, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"FFT带阻滤波失败: {str(e)}")
