#!/usr/bin/env python3
"""
LitLSTMSTAModel使用示例

这个示例展示了如何使用LitLSTMSTAModel进行训练、验证和预测。
包含了完整的训练流程和注意力权重可视化。
"""

import torch
import torch.nn as nn
import lightning as L
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, TensorDataset
from lightning.pytorch.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
)
from lightning.pytorch.loggers import TensorBoardLogger

# 导入我们的模型
from src.model.lstm_sta import LitLSTMSTA


def create_synthetic_data(num_samples=1000, input_size=64, output_size=1):
    """
    创建合成数据用于演示

    Args:
        num_samples: 样本数量
        input_size: 输入特征维度
        output_size: 输出特征维度

    Returns:
        X: 输入数据 [num_samples, input_size]
        y: 目标数据 [num_samples, output_size]
    """
    # 创建具有时间序列特性的合成数据
    X = torch.randn(num_samples, input_size)

    # 创建一些非线性关系
    # 假设输出是输入的某种复杂函数
    y = torch.sin(X.mean(dim=1, keepdim=True)) + 0.1 * torch.randn(
        num_samples, output_size
    )

    return X, y


def create_data_loaders(X, y, batch_size=32, train_ratio=0.8, val_ratio=0.1):
    """
    创建数据加载器

    Args:
        X: 输入数据
        y: 目标数据
        batch_size: 批次大小
        train_ratio: 训练集比例
        val_ratio: 验证集比例

    Returns:
        train_loader, val_loader, test_loader
    """
    num_samples = len(X)
    train_size = int(num_samples * train_ratio)
    val_size = int(num_samples * val_ratio)
    test_size = num_samples - train_size - val_size

    # 随机打乱数据
    indices = torch.randperm(num_samples)

    # 分割数据
    train_indices = indices[:train_size]
    val_indices = indices[train_size : train_size + val_size]
    test_indices = indices[train_size + val_size :]

    # 创建数据集
    train_dataset = TensorDataset(X[train_indices], y[train_indices])
    val_dataset = TensorDataset(X[val_indices], y[val_indices])
    test_dataset = TensorDataset(X[test_indices], y[test_indices])

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    return train_loader, val_loader, test_loader


def train_lstm_sta_model():
    """训练LSTM-STA模型的完整示例"""
    print("开始LSTM-STA模型训练示例...")

    # 模型参数
    input_size = 64
    lstm_input_size = 8
    lstm_hidden_size = 64
    output_size = 1
    sequence_length = 8

    # 验证参数约束
    assert input_size == sequence_length * lstm_input_size, "参数约束检查失败"

    # 创建数据
    print("创建合成数据...")
    X, y = create_synthetic_data(
        num_samples=2000, input_size=input_size, output_size=output_size
    )
    train_loader, val_loader, test_loader = create_data_loaders(X, y, batch_size=64)

    print(f"训练集大小: {len(train_loader.dataset)}")
    print(f"验证集大小: {len(val_loader.dataset)}")
    print(f"测试集大小: {len(test_loader.dataset)}")

    # 创建模型
    print("创建LSTM-STA模型...")
    model = LitLSTMSTA(
        input_size=input_size,
        lstm_input_size=lstm_input_size,
        lstm_hidden_size=lstm_hidden_size,
        output_size=output_size,
        sequence_length=sequence_length,
        dropout=0.1,
        learning_rate=1e-3,
        weight_decay=1e-5,
    )

    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 设置回调
    checkpoint_callback = ModelCheckpoint(
        monitor="val_loss",
        filename="lstm-sta-{epoch:02d}-{val_loss:.4f}",
        save_top_k=3,
        mode="min",
    )

    early_stopping = EarlyStopping(
        monitor="val_loss",
        patience=10,
        mode="min",
    )

    lr_monitor = LearningRateMonitor(logging_interval="epoch")

    # 设置日志记录器
    logger = TensorBoardLogger("logs", name="lstm_sta")

    # 创建训练器
    trainer = L.Trainer(
        max_epochs=50,
        callbacks=[checkpoint_callback, early_stopping, lr_monitor],
        logger=logger,
        accelerator="auto",
        devices=1,
        gradient_clip_val=1.0,
        enable_progress_bar=True,
    )

    # 训练模型
    print("开始训练...")
    trainer.fit(
        model=model,
        train_dataloaders=train_loader,
        val_dataloaders=val_loader,
    )

    # 测试模型
    print("开始测试...")
    test_results = trainer.test(model=model, dataloaders=test_loader)

    print(f"测试结果: {test_results[0]}")

    return model, test_loader


def visualize_attention_weights(model, test_loader, num_samples=5):
    """
    可视化注意力权重

    Args:
        model: 训练好的模型
        test_loader: 测试数据加载器
        num_samples: 可视化的样本数量
    """
    print("可视化注意力权重...")

    model.eval()

    # 获取一些测试样本
    test_batch = next(iter(test_loader))
    X_test, y_test = test_batch

    # 只取前几个样本
    X_sample = X_test[:num_samples]
    y_sample = y_test[:num_samples]

    with torch.no_grad():
        # 获取预测结果
        predictions = model(X_sample)

        # 获取注意力权重
        spatial_weights, temporal_weights = model.get_attention_weights(X_sample)

    # 创建可视化
    fig, axes = plt.subplots(num_samples, 3, figsize=(15, 3 * num_samples))
    if num_samples == 1:
        axes = axes.reshape(1, -1)

    for i in range(num_samples):
        # 空间注意力权重热图
        im1 = axes[i, 0].imshow(spatial_weights[i].cpu().numpy(), cmap="Blues")
        axes[i, 0].set_title(f"样本 {i+1} - 空间注意力权重")
        axes[i, 0].set_xlabel("时间步")
        axes[i, 0].set_ylabel("时间步")
        plt.colorbar(im1, ax=axes[i, 0])

        # 时间注意力权重
        axes[i, 1].bar(
            range(len(temporal_weights[i])), temporal_weights[i].cpu().numpy()
        )
        axes[i, 1].set_title(f"样本 {i+1} - 时间注意力权重")
        axes[i, 1].set_xlabel("时间步")
        axes[i, 1].set_ylabel("注意力权重")

        # 预测结果对比
        axes[i, 2].bar(
            ["真实值", "预测值"],
            [y_sample[i].item(), predictions[i].item()],
            color=["blue", "orange"],
        )
        axes[i, 2].set_title(f"样本 {i+1} - 预测结果对比")
        axes[i, 2].set_ylabel("值")

    plt.tight_layout()
    plt.savefig("lstm_sta_attention_visualization.png", dpi=300, bbox_inches="tight")
    plt.show()

    print("注意力权重可视化已保存为 'lstm_sta_attention_visualization.png'")


def main():
    """主函数"""
    print("LSTM-STA模型完整使用示例")
    print("=" * 50)

    try:
        # 训练模型
        model, test_loader = train_lstm_sta_model()

        # 可视化注意力权重
        visualize_attention_weights(model, test_loader, num_samples=3)

        print("\n✅ 示例运行完成！")
        print("你可以在 'logs' 目录中查看TensorBoard日志")
        print("运行 'tensorboard --logdir logs' 来启动TensorBoard")

    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
