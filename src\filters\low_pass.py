"""
低通滤波器模块

该模块实现了数字低通滤波器，用于去除信号中的高频噪声，保留低频成分。
支持多种滤波器类型：Butterworth、Chebyshev I、Chebyshev II、Elliptic等。
"""

import numpy as np
import pandas as pd
from scipy import signal
from typing import Optional, Any


def low_pass_filter(
    data: pd.Series,
    cutoff_freq: float,
    sampling_freq: float,
    order: int = 4,
    filter_type: str = "butterworth",
    rp: Optional[float] = None,
    rs: Optional[float] = None,
) -> pd.Series:
    """
    应用低通滤波器到输入信号

    参数:
        data (pd.Series): 输入信号数据
        cutoff_freq (float): 截止频率 (Hz)
        sampling_freq (float): 采样频率 (Hz)
        order (int): 滤波器阶数，默认为4
        filter_type (str): 滤波器类型，可选：
            - 'butterworth': Butterworth滤波器（默认）
            - 'chebyshev1': Chebyshev I型滤波器
            - 'chebyshev2': Chebyshev II型滤波器
            - 'elliptic': 椭圆滤波器
        rp (float, optional): 通带最大衰减 (dB)，用于Chebyshev I和椭圆滤波器
        rs (float, optional): 阻带最小衰减 (dB)，用于Chebyshev II和椭圆滤波器

    返回:
        pd.Series: 滤波后的信号数据

    异常:
        ValueError: 当参数不合法时抛出

    示例:
        >>> import pandas as pd
        >>> import numpy as np
        >>>
        >>> # 创建测试信号
        >>> t = np.linspace(0, 1, 1000)
        >>> signal_data = np.sin(2 * np.pi * 5 * t) + 0.5 * np.sin(2 * np.pi * 50 * t)
        >>> data = pd.Series(signal_data)
        >>>
        >>> # 应用低通滤波器
        >>> filtered_data = low_pass_filter(data, cutoff_freq=10, sampling_freq=1000)
    """

    # 参数验证
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if cutoff_freq <= 0:
        raise ValueError("截止频率必须大于0")

    if sampling_freq <= 0:
        raise ValueError("采样频率必须大于0")

    if cutoff_freq >= sampling_freq / 2:
        raise ValueError("截止频率必须小于奈奎斯特频率 (采样频率的一半)")

    if order <= 0:
        raise ValueError("滤波器阶数必须大于0")

    # 计算归一化截止频率
    nyquist_freq = sampling_freq / 2
    normalized_cutoff = cutoff_freq / nyquist_freq

    try:
        # 确保 Wn 在 (0, 1) 区间内
        if not 0 < normalized_cutoff < 1:
            raise ValueError("归一化截止频率必须在 0 和 1 之间！")

        # 根据滤波器类型设计滤波器
        if filter_type.lower() == "butterworth":
            b, a = signal.butter(  # type: ignore
                order, normalized_cutoff, btype="lowpass", analog=False
            )

        elif filter_type.lower() == "chebyshev1":
            if rp is None:
                rp = 1.0  # 默认通带衰减1dB
            b, a = signal.cheby1(  # type: ignore
                order, rp, normalized_cutoff, btype="lowpass", analog=False
            )

        elif filter_type.lower() == "chebyshev2":
            if rs is None:
                rs = 40.0  # 默认阻带衰减40dB
            b, a = signal.cheby2(  # type: ignore
                order, rs, normalized_cutoff, btype="low", analog=False
            )

        elif filter_type.lower() == "elliptic":
            if rp is None:
                rp = 1.0  # 默认通带衰减1dB
            if rs is None:
                rs = 40.0  # 默认阻带衰减40dB
            b, a = signal.ellip(  # type: ignore
                order, rp, rs, normalized_cutoff, btype="low", analog=False
            )

        else:
            raise ValueError(f"不支持的滤波器类型: {filter_type}")

        # 应用滤波器
        filtered_data = signal.filtfilt(b, a, data.values)

        # 创建结果Series，保持原始索引
        result = pd.Series(filtered_data, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"滤波器设计或应用失败: {str(e)}")


def moving_average_filter(data: pd.Series, window_size: int) -> pd.Series:
    """
    简单移动平均滤波器（低通滤波的一种简单实现）

    参数:
        data (pd.Series): 输入信号数据
        window_size (int): 移动平均窗口大小

    返回:
        pd.Series: 滤波后的信号数据
    """
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if window_size <= 0:
        raise ValueError("窗口大小必须大于0")

    if window_size > len(data):
        raise ValueError("窗口大小不能大于数据长度")

    # 使用pandas的rolling方法计算移动平均
    filtered_data = data.rolling(window=window_size, center=True).mean()

    # 处理边界值（使用前向填充和后向填充）
    filtered_data = filtered_data.bfill().ffill()

    return filtered_data


def exponential_smoothing_filter(data: pd.Series, alpha: float) -> pd.Series:
    """
    指数平滑滤波器（低通滤波的另一种实现）

    参数:
        data (pd.Series): 输入信号数据
        alpha (float): 平滑系数，范围[0, 1]，值越小平滑效果越强

    返回:
        pd.Series: 滤波后的信号数据
    """
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if not 0 <= alpha <= 1:
        raise ValueError("平滑系数alpha必须在[0, 1]范围内")

    # 使用pandas的ewm方法进行指数加权移动平均
    filtered_data = data.ewm(alpha=alpha, adjust=False).mean()

    return filtered_data
