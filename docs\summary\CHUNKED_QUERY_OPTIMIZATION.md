# InfluxDB 分块查询优化总结

## 概述

对 `src/utils/influxql_helper.py` 文件中的 `batch_query_aligned_df` 方法进行了重大优化，实现了智能分块查询机制，解决了大数据量查询时的内存溢出和超时问题。

## 主要功能

### 1. 智能分块策略

- **自动判断**: 根据时间跨度自动决定是否需要分块查询
- **可配置分块大小**: 新增 `chunk_size` 参数，支持 pandas 时间频率格式
- **向后兼容**: 保持原有方法签名，现有代码无需修改

### 2. 分块查询机制

- **时间分割**: 将大时间范围自动分割成多个小的时间块
- **顺序查询**: 按时间顺序查询每个分块，避免内存峰值
- **智能合并**: 自动合并所有分块结果，去除重复时间戳

### 3. 错误处理和日志

- **进度跟踪**: 详细的分块查询进度日志
- **容错机制**: 单个分块失败不影响整体查询
- **性能监控**: 记录查询时间和数据量信息

## 方法签名

```python
def batch_query_aligned_df(
    self,
    measurements,
    field: str | list[str] = "value",
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    time_interval="1s",
    fill_method="linear",
    resample=True,
    chunk_size: str = "1h",  # 新增参数
) -> pd.DataFrame | pd.Series | None:
```

### 新增参数说明

- `chunk_size`: 分块大小，支持以下格式：
  - `"30min"` - 30分钟
  - `"1h"` - 1小时（默认）
  - `"2h"` - 2小时
  - `"1d"` - 1天
  - 其他 pandas 支持的时间频率格式

## 使用示例

### 基本用法（向后兼容）

```python
from src.utils.influxql_helper import InfluxQuery

influx_query = InfluxQuery(host, port, username, password, database)

# 原有调用方式完全兼容
df = influx_query.batch_query_aligned_df(
    measurements=["sensor1", "sensor2"],
    start_time=start_time,
    end_time=end_time,
    time_interval="1min",
    fill_method="linear"
)
```

### 自定义分块大小

```python
# 使用30分钟分块（适合高频数据）
df = influx_query.batch_query_aligned_df(
    measurements=measurements,
    start_time=start_time,
    end_time=end_time,
    time_interval="1s",
    fill_method="linear",
    chunk_size="30min"
)

# 使用2小时分块（适合长时间范围查询）
df = influx_query.batch_query_aligned_df(
    measurements=measurements,
    start_time=start_time,
    end_time=end_time,
    time_interval="1min",
    fill_method="linear",
    chunk_size="2h"
)
```

### 多字段分块查询

```python
# 多字段查询也支持分块
df = influx_query.batch_query_aligned_df(
    measurements=["furnace_zone_1", "furnace_zone_2"],
    field=["temperature", "pressure", "oxygen_level"],
    start_time=start_time,
    end_time=end_time,
    time_interval="1min",
    fill_method="linear",
    chunk_size="1h"
)
```

## 工作原理

### 1. 智能判断逻辑

```python
time_span = end_time - start_time
chunk_timedelta = pd.Timedelta(chunk_size)

if time_span <= chunk_timedelta:
    # 使用直接查询
    return self._query_without_chunking(...)
else:
    # 使用分块查询
    return self._query_with_chunking(...)
```

### 2. 分块查询流程

1. **计算分块数量**: 根据时间跨度和分块大小计算总块数
2. **逐块查询**: 按时间顺序查询每个时间块
3. **数据合并**: 将所有分块结果按时间戳合并
4. **去重处理**: 移除可能的重复时间戳
5. **统一处理**: 应用重采样和填充逻辑

### 3. 内存优化

- **流式处理**: 不同时加载所有分块数据
- **及时释放**: 每个分块处理完后立即释放内存
- **增量合并**: 使用 pandas concat 进行高效合并

## 性能优势

### 1. 内存使用优化

- **分块加载**: 避免一次性加载大量数据
- **内存峰值控制**: 内存使用量与分块大小成正比，而非总数据量

### 2. 查询超时避免

- **小块查询**: 每个分块查询时间可控
- **容错机制**: 单个分块失败不影响整体

### 3. 网络传输优化

- **分批传输**: 减少单次网络传输压力
- **并发潜力**: 为未来并发查询优化预留空间

## 日志示例

```
INFO - 时间跨度(6:00:00)较大，使用分块查询，分块大小: 1h
INFO - 开始分块查询，预计分为 6 个块
INFO - 正在查询第 1/6 块: 2025-04-16 00:00:00 到 2025-04-16 01:00:00
INFO - 第 1 块查询成功，获得 3600 行数据
INFO - 正在查询第 2/6 块: 2025-04-16 01:00:00 到 2025-04-16 02:00:00
INFO - 第 2 块查询成功，获得 3600 行数据
...
INFO - 开始合并 6 个分块的数据
INFO - 分块查询完成，最终获得 21600 行数据
```

## 配置建议

### 分块大小选择

- **高频数据** (秒级): 建议使用 `"30min"` 或 `"1h"`
- **中频数据** (分钟级): 建议使用 `"1h"` 或 `"2h"`
- **低频数据** (小时级): 建议使用 `"1d"` 或更大

### 时间范围建议

- **小于1小时**: 自动使用直接查询
- **1-24小时**: 使用 `"1h"` 分块
- **1-7天**: 使用 `"2h"` 或 `"4h"` 分块
- **超过7天**: 使用 `"1d"` 分块

## 注意事项

1. **分块边界**: 可能在分块边界处出现轻微的数据重复，已自动处理
2. **时区处理**: 保持与原有逻辑一致，使用 UTC 时间
3. **错误恢复**: 单个分块失败会记录错误但继续处理其他分块
4. **内存监控**: 建议在生产环境中监控内存使用情况

## 测试

使用 `test_chunked_query.py` 脚本可以测试分块查询功能：

```bash
python test_chunked_query.py
```

测试包括：
- 小时间范围查询（不分块）
- 大时间范围查询（分块）
- 多字段分块查询
- 不同分块大小的性能对比

## 总结

这次优化显著提升了 `batch_query_aligned_df` 方法处理大数据量查询的能力，同时保持了完全的向后兼容性。通过智能分块机制，用户可以安全地查询大时间范围的数据，而无需担心内存溢出或查询超时问题。
