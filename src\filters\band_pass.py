"""
带通滤波器模块

该模块实现了数字带通滤波器，用于保留信号中特定频率范围的成分，去除其他频率成分。
支持多种滤波器类型：Butterworth、Chebyshev I、Chebyshev II、Elliptic等。
"""

import numpy as np
import pandas as pd
from scipy import signal
from typing import Optional


def band_pass_filter(
    data: pd.Series,
    low_cutoff: float,
    high_cutoff: float,
    sampling_freq: float,
    order: int = 4,
    filter_type: str = "butterworth",
    rp: Optional[float] = None,
    rs: Optional[float] = None,
) -> pd.Series:
    """
    应用带通滤波器到输入信号

    参数:
        data (pd.Series): 输入信号数据
        low_cutoff (float): 低截止频率 (Hz)
        high_cutoff (float): 高截止频率 (Hz)
        sampling_freq (float): 采样频率 (Hz)
        order (int): 滤波器阶数，默认为4
        filter_type (str): 滤波器类型，可选：
            - 'butterworth': Butterworth滤波器（默认）
            - 'chebyshev1': Chebyshev I型滤波器
            - 'chebyshev2': Chebyshev II型滤波器
            - 'elliptic': 椭圆滤波器
        rp (float, optional): 通带最大衰减 (dB)，用于Chebyshev I和椭圆滤波器
        rs (float, optional): 阻带最小衰减 (dB)，用于Chebyshev II和椭圆滤波器

    返回:
        pd.Series: 滤波后的信号数据

    异常:
        ValueError: 当参数不合法时抛出

    示例:
        >>> import pandas as pd
        >>> import numpy as np
        >>>
        >>> # 创建测试信号
        >>> t = np.linspace(0, 1, 1000)
        >>> signal_data = np.sin(2 * np.pi * 5 * t) + np.sin(2 * np.pi * 25 * t) + np.sin(2 * np.pi * 50 * t)
        >>> data = pd.Series(signal_data)
        >>>
        >>> # 应用带通滤波器，保留20-30Hz的信号
        >>> filtered_data = band_pass_filter(data, low_cutoff=20, high_cutoff=30, sampling_freq=1000)
    """

    # 参数验证
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if low_cutoff <= 0:
        raise ValueError("低截止频率必须大于0")

    if high_cutoff <= 0:
        raise ValueError("高截止频率必须大于0")

    if low_cutoff >= high_cutoff:
        raise ValueError("低截止频率必须小于高截止频率")

    if sampling_freq <= 0:
        raise ValueError("采样频率必须大于0")

    if high_cutoff >= sampling_freq / 2:
        raise ValueError("高截止频率必须小于奈奎斯特频率 (采样频率的一半)")

    if order <= 0:
        raise ValueError("滤波器阶数必须大于0")

    # 计算归一化截止频率
    nyquist_freq = sampling_freq / 2
    normalized_low = low_cutoff / nyquist_freq
    normalized_high = high_cutoff / nyquist_freq

    try:
        # 根据滤波器类型设计滤波器
        if filter_type.lower() == "butterworth":
            b, a = signal.butter(order, [normalized_low, normalized_high], btype="band", analog=False)  # type: ignore

        elif filter_type.lower() == "chebyshev1":
            if rp is None:
                rp = 1.0  # 默认通带衰减1dB
            b, a = signal.cheby1(order, rp, [normalized_low, normalized_high], btype="band", analog=False)  # type: ignore

        elif filter_type.lower() == "chebyshev2":
            if rs is None:
                rs = 40.0  # 默认阻带衰减40dB
            b, a = signal.cheby2(order, rs, [normalized_low, normalized_high], btype="band", analog=False)  # type: ignore

        elif filter_type.lower() == "elliptic":
            if rp is None:
                rp = 1.0  # 默认通带衰减1dB
            if rs is None:
                rs = 40.0  # 默认阻带衰减40dB
            b, a = signal.ellip(  # type: ignore
                order,
                rp,
                rs,
                [normalized_low, normalized_high],
                btype="band",
                analog=False,
            )  # type: ignore

        else:
            raise ValueError(f"不支持的滤波器类型: {filter_type}")

        # 应用滤波器
        filtered_data = signal.filtfilt(b, a, data.values)

        # 创建结果Series，保持原始索引
        result = pd.Series(filtered_data, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"滤波器设计或应用失败: {str(e)}")


def fft_band_pass_filter(
    data: pd.Series, low_cutoff: float, high_cutoff: float, sampling_freq: float
) -> pd.Series:
    """
    基于FFT的带通滤波器（频域滤波）

    参数:
        data (pd.Series): 输入信号数据
        low_cutoff (float): 低截止频率 (Hz)
        high_cutoff (float): 高截止频率 (Hz)
        sampling_freq (float): 采样频率 (Hz)

    返回:
        pd.Series: 滤波后的信号数据
    """
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if low_cutoff <= 0:
        raise ValueError("低截止频率必须大于0")

    if high_cutoff <= 0:
        raise ValueError("高截止频率必须大于0")

    if low_cutoff >= high_cutoff:
        raise ValueError("低截止频率必须小于高截止频率")

    if sampling_freq <= 0:
        raise ValueError("采样频率必须大于0")

    try:
        # 计算FFT
        fft_data = np.fft.fft(data.values)  # type: ignore
        freqs = np.fft.fftfreq(len(data), 1 / sampling_freq)

        # 创建频域滤波器
        filter_mask = np.zeros_like(freqs, dtype=bool)
        filter_mask[(np.abs(freqs) >= low_cutoff) & (np.abs(freqs) <= high_cutoff)] = (
            True
        )

        # 应用滤波器
        filtered_fft = fft_data * filter_mask

        # 逆FFT
        filtered_data = np.real(np.fft.ifft(filtered_fft))

        # 创建结果Series，保持原始索引
        result = pd.Series(filtered_data, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"FFT带通滤波失败: {str(e)}")
