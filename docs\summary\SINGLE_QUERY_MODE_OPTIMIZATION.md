# 单个查询模式优化总结

## 问题背景

在使用 `batch_query_aligned_df` 方法时，发现字符串类型的数据被丢弃的问题是由于 InfluxDB v1 的特性导致的：

### InfluxDB v1 联合查询的限制
1. **数据类型冲突**：当多个 measurements 被联合查询时，InfluxDB 引擎会优先选择第一个遇到的数据类型作为预期类型
2. **数据丢弃**：后续不匹配该类型的数据将被自动丢弃
3. **类型限制**：无法在单次查询中混合不同数据类型的 measurements

### 具体问题
- **Measurement A**（数值类型）+ **Measurement B**（字符串类型）→ 只返回 Measurement A
- **Measurement C**（布尔类型）+ **Measurement D**（数值类型）→ 只返回第一个遇到的类型

## 解决方案

### 核心策略：废弃联合查询，采用单个查询模式

1. **废弃联合查询方式**：不再使用 InfluxQueryWrapper 的多 measurements 联合查询功能
2. **改为单个查询模式**：对每个 measurement 分别进行单独查询
3. **数据组合策略**：将所有单个查询的结果按时间戳进行对齐和合并
4. **保持现有功能**：确保时间戳对齐、重采样、填充等现有功能正常工作
5. **向后兼容**：保持方法签名和返回格式不变

## 实现细节

### 1. 修改 `_query_without_chunking` 方法

<augment_code_snippet path="src/utils/influxql_helper.py" mode="EXCERPT">
```python
def _query_without_chunking(self, measurements, field, start_time, end_time, time_interval, fill_method, resample):
    """不使用分块的单个查询方法"""
    self.logger.info(f"开始单个查询模式，查询 {len(measurements)} 个测量点")
    
    # 对每个measurement分别进行单独查询
    measurement_results = {}
    successful_queries = 0
    
    for measurement in measurements:
        try:
            # 为每个measurement创建独立的查询对象
            qw = InfluxQueryWrapper(measurement, field=field)  # 单个measurement
            
            # 添加时间范围条件
            if start_time:
                qw.gt("time", start_time.strftime("%Y-%m-%dT%H:%M:%SZ"))
            if end_time:
                qw.le("time", end_time.strftime("%Y-%m-%dT%H:%M:%SZ"))
            
            # 执行单个measurement查询
            result = self.query_df_by_wrapper(qw)
            
            if result is not None:
                measurement_results[measurement] = result
                successful_queries += 1
                
        except Exception as e:
            self.logger.error(f"测量点 {measurement} 查询失败: {str(e)}")
            continue
    
    # 合并所有单个查询的结果
    return self._combine_measurement_results(measurement_results, field, time_interval, fill_method, resample)
```
</augment_code_snippet>

### 2. 修改 `_query_single_chunk` 方法

<augment_code_snippet path="src/utils/influxql_helper.py" mode="EXCERPT">
```python
def _query_single_chunk(self, measurements, field, start_time, end_time):
    """查询单个时间块的数据，使用单个查询模式"""
    # 对每个measurement分别进行单独查询
    measurement_results = {}
    
    for measurement in measurements:
        try:
            # 为每个measurement创建独立的查询对象
            qw = InfluxQueryWrapper(measurement, field=field)  # 单个measurement
            qw.gt("time", start_time.strftime("%Y-%m-%dT%H:%M:%SZ"))
            qw.le("time", end_time.strftime("%Y-%m-%dT%H:%M:%SZ"))
            
            # 执行单个measurement查询
            result = self.query_df_by_wrapper(qw)
            
            if result is not None:
                measurement_results[measurement] = result
                
        except Exception as e:
            self.logger.warning(f"分块查询中测量点 {measurement} 失败: {str(e)}")
            continue
    
    # 合并所有单个查询的结果，但不进行重采样和填充（在最后统一处理）
    return self._combine_measurement_results(measurement_results, field, None, None, False)
```
</augment_code_snippet>

### 3. 新增 `_combine_measurement_results` 方法

这是核心的数据合并方法，负责将多个单独查询的结果合并成统一的 DataFrame：

<augment_code_snippet path="src/utils/influxql_helper.py" mode="EXCERPT">
```python
def _combine_measurement_results(self, measurement_results, field, time_interval, fill_method, resample):
    """合并多个单独查询的measurement结果"""
    if not measurement_results:
        self.logger.warning("没有可合并的measurement结果")
        return pd.DataFrame()
    
    self.logger.info(f"开始合并 {len(measurement_results)} 个measurement的查询结果")
    
    # 创建一个空的DataFrame用于存储合并结果
    aligned_df = pd.DataFrame()
    field_list = field if isinstance(field, (list, tuple)) else [field]
    
    for measurement_name, result in measurement_results.items():
        # 处理单个measurement的结果
        # 进行列重命名和数据类型统计
        # 使用 pd.merge 进行时间戳对齐合并
        
    # 时间戳重排序和重采样填充
    return aligned_df
```
</augment_code_snippet>

## 优化效果

### ✅ 解决的问题

1. **数据类型兼容性**：完全解决了 InfluxDB v1 联合查询的数据类型冲突问题
2. **数据完整性**：所有数据类型的 measurement 都能被正确查询和返回
3. **混合类型支持**：支持数值、字符串、布尔、时间等所有数据类型的混合查询

### ✅ 测试验证结果

```
测试1: 混合数据类型 measurement（单个查询模式）
结果形状: (6, 4)
列名: ['temperature_sensor_1', 'device_status_1', 'alarm_switch_1', 'mixed_device_1']
数据类型:
  temperature_sensor_1: float64    # 数值类型 ✅
  device_status_1: object          # 字符串类型 ✅
  alarm_switch_1: bool             # 布尔类型 ✅
  mixed_device_1: float64          # 混合类型 ✅

✅ 所有 measurement 都被正确包含
```

### ✅ 保持的功能

1. **向后兼容性**：方法签名和返回格式完全不变
2. **分块查询**：分块查询功能正常工作，支持大时间范围查询
3. **时间戳对齐**：使用 `pd.merge` 进行精确的时间戳对齐
4. **重采样填充**：智能重采样和混合数据类型填充功能正常
5. **错误处理**：单个 measurement 查询失败不影响其他 measurement

### ✅ 性能考虑

1. **查询数量增加**：从 1 次联合查询变为 N 次单独查询
2. **网络开销**：网络请求次数增加，但单次请求更小更快
3. **容错能力**：单个查询失败不影响整体，提高了系统稳定性
4. **并发潜力**：为未来的并发查询优化预留了空间

## 使用示例

### 基本用法（完全兼容）

```python
# 原有代码无需任何修改
df = influx_query.batch_query_aligned_df(
    measurements=["sensor1", "sensor2"],
    start_time=start_time,
    end_time=end_time,
    time_interval="1min",
    fill_method="linear"
)
```

### 混合数据类型查询（现在完全支持）

```python
# 现在可以安全查询混合数据类型，不会有数据丢失
mixed_measurements = [
    "temperature_sensor_1",  # 数值类型
    "device_status_1",       # 字符串类型
    "alarm_switch_1",        # 布尔类型
    "mixed_device_1"         # 混合类型
]

df = influx_query.batch_query_aligned_df(
    measurements=mixed_measurements,
    field="value",
    start_time=start_time,
    end_time=end_time,
    time_interval="10s",
    fill_method="linear",
    chunk_size="1h"
)

# 结果包含所有数据类型的列，无数据丢失 ✅
print(f"列名: {list(df.columns)}")
# 输出: ['temperature_sensor_1', 'device_status_1', 'alarm_switch_1', 'mixed_device_1']
```

### 分块查询（自动适配）

```python
# 分块查询也使用单个查询模式，避免数据类型冲突
df = influx_query.batch_query_aligned_df(
    measurements=mixed_measurements,
    field="value",
    start_time=start_time,
    end_time=end_time + timedelta(hours=6),  # 大时间范围，触发分块
    time_interval="1min",
    fill_method="linear",
    chunk_size="1h"  # 每小时一块，每块内使用单个查询模式
)
```

## 注意事项

1. **查询性能**：查询次数会增加，但单次查询更快，总体性能影响较小
2. **网络延迟**：在高延迟网络环境下，多次查询可能会增加总耗时
3. **数据库负载**：对 InfluxDB 的查询请求数量会增加
4. **容错机制**：单个 measurement 查询失败会记录错误但不中断整体流程

## 总结

这次优化彻底解决了 InfluxDB v1 联合查询的数据类型兼容性问题：

- **✅ 根本解决**：从根本上避免了 InfluxDB v1 的数据类型冲突问题
- **✅ 数据完整性**：所有数据类型的 measurement 都能被正确处理
- **✅ 向后兼容**：现有代码无需任何修改
- **✅ 功能保持**：所有现有功能（分块、重采样、填充等）正常工作
- **✅ 错误处理**：更强的容错能力和错误恢复机制

用户现在可以安全地查询包含任意数据类型组合的 measurement，而无需担心数据丢失或类型冲突问题。
