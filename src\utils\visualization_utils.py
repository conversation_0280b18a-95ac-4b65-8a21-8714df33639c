"""
可视化工具模块

提供模型预测结果的可视化功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging


def setup_plot_style():
    """设置绘图样式"""
    plt.style.use("default")
    sns.set_palette("husl")
    plt.rcParams["font.sans-serif"] = ["SimHei", "DejaVu Sans"]
    plt.rcParams["axes.unicode_minus"] = False
    plt.rcParams["figure.dpi"] = 300
    plt.rcParams["font.size"] = 10
    plt.rcParams["axes.titlesize"] = 12
    plt.rcParams["axes.labelsize"] = 11
    plt.rcParams["legend.fontsize"] = 9
    plt.rcParams["xtick.labelsize"] = 9
    plt.rcParams["ytick.labelsize"] = 9


def categorize_columns(target_columns: List[str]) -> Dict[str, List[str]]:
    """
    将目标列按类型分类

    Args:
        target_columns: 目标列名列表

    Returns:
        分类后的列名字典
    """
    categories = {
        "furnace_temp": [],  # 炉温数据
        "strip_temp": [],  # 板温数据
        "other": [],  # 其他数据
    }

    for col in target_columns:
        if col.startswith("STRIP_TEMP_"):
            categories["strip_temp"].append(col)
        elif col.startswith("TEMP_"):
            categories["furnace_temp"].append(col)
        else:
            categories["other"].append(col)

    return categories


def plot_prediction_comparison(
    actual: np.ndarray,
    predictions: np.ndarray,
    target_columns: List[str],
    file_index: int = 0,
    save_path: Optional[str] = None,
    show_confidence: bool = True,
) -> None:
    """
    绘制预测结果与实际结果的对比图表
    为炉温数据和板温数据分别创建独立的图表，每个预测列都有自己的子图

    Args:
        actual: 实际值数组，形状为 (n_samples, n_targets)
        predictions: 预测值数组，形状为 (n_samples, n_targets)
        target_columns: 目标列名列表
        file_index: 文件索引，用于标题显示
        save_path: 保存路径，如果为None则不保存
        show_confidence: 是否显示置信区间
        max_samples: 最大显示样本数，用于控制图表清晰度
    """
    logger = logging.getLogger(__name__)

    try:
        setup_plot_style()

        # 分类列
        categories = categorize_columns(target_columns)
        x_axis = np.arange(len(actual))

        # 绘制炉温数据图表
        if categories["furnace_temp"]:
            _plot_category_chart(
                actual=actual,
                predictions=predictions,
                category_columns=categories["furnace_temp"],
                all_columns=target_columns,
                x_axis=x_axis,
                category_name="炉温数据",
                confidence_interval=10 if show_confidence else None,
                file_index=file_index,
                save_path=save_path,
                chart_suffix="furnace_temp",
            )

        # 绘制板温数据图表
        if categories["strip_temp"]:
            _plot_category_chart(
                actual=actual,
                predictions=predictions,
                category_columns=categories["strip_temp"],
                all_columns=target_columns,
                x_axis=x_axis,
                category_name="板温数据",
                confidence_interval=5 if show_confidence else None,
                file_index=file_index,
                save_path=save_path,
                chart_suffix="strip_temp",
            )

        # 绘制其他数据图表
        if categories["other"]:
            _plot_category_chart(
                actual=actual,
                predictions=predictions,
                category_columns=categories["other"],
                all_columns=target_columns,
                x_axis=x_axis,
                category_name="其他数据",
                confidence_interval=None,
                file_index=file_index,
                save_path=save_path,
                chart_suffix="other",
            )

    except Exception as e:
        logger.error(f"绘制预测对比图失败: {e}")
        raise


def _plot_category_chart(
    actual: np.ndarray,
    predictions: np.ndarray,
    category_columns: List[str],
    all_columns: List[str],
    x_axis: np.ndarray,
    category_name: str,
    confidence_interval: Optional[float] = None,
    file_index: int = 0,
    save_path: Optional[str] = None,
    chart_suffix: str = "",
) -> None:
    """
    为特定类别的数据创建独立图表，每个预测列都有自己的子图

    Args:
        actual: 实际值数组
        predictions: 预测值数组
        category_columns: 当前类别的列名
        all_columns: 所有列名
        x_axis: x轴数据
        category_name: 类别名称
        confidence_interval: 置信区间范围（±度数）
        file_index: 文件索引
        save_path: 保存路径
        chart_suffix: 图表后缀名
    """
    logger = logging.getLogger(__name__)

    if not category_columns:
        return

    # 计算子图布局 - 改为纵向排列（Nx1）
    n_rows = len(category_columns)
    n_cols_layout = 1

    # 根据子图数量调整图表尺寸
    fig_width = 24  # 固定宽度
    fig_height = max(6, n_rows * 4) + 2  # 每个子图高度约4英寸

    # 创建图表 - 纵向排列
    fig, axes = plt.subplots(n_rows, n_cols_layout, figsize=(fig_width, fig_height))

    # 确保axes是一维数组（便于索引）
    if n_rows == 1:
        axes = [axes]  # 单个子图时转换为列表
    elif n_rows > 1:
        axes = axes.flatten()  # 多个子图时展平为一维数组

    # 定义颜色方案
    colors = sns.color_palette("Set2", 3)  # 实际值、预测值、置信区间

    for i, col in enumerate(category_columns):
        ax = axes[i]  # 直接使用索引，因为是纵向排列

        col_idx = all_columns.index(col)

        # 绘制置信区间（如果有）
        if confidence_interval is not None:
            actual_col = actual[:, col_idx]
            upper_bound = actual_col + confidence_interval
            lower_bound = actual_col - confidence_interval

            ax.fill_between(
                x_axis,
                lower_bound,
                upper_bound,
                color=colors[2],
                alpha=0.3,
                label=f"置信区间 (±{confidence_interval}°C)",
                zorder=1,
            )

        # 绘制实际值
        ax.plot(
            x_axis,
            actual[:, col_idx],
            label="实际值",
            color=colors[0],
            linewidth=2.0,
            alpha=0.9,
            zorder=3,
        )

        # 绘制预测值
        ax.plot(
            x_axis,
            predictions[:, col_idx],
            label="预测值",
            color=colors[1],
            linewidth=2.0,
            linestyle="--",
            alpha=0.9,
            zorder=2,
        )

        # 设置子图样式
        ax.set_title(f"{col}", fontsize=12, fontweight="bold", pad=10)
        ax.set_xlabel("样本序号", fontsize=10)
        ax.set_ylabel("温度 (°C)", fontsize=10)
        ax.grid(True, alpha=0.4, linestyle="-", linewidth=0.5)
        ax.legend(loc="upper right", fontsize=9, framealpha=0.9)

        # 优化坐标轴
        ax.tick_params(axis="both", which="major", labelsize=9)
        ax.spines["top"].set_visible(False)
        ax.spines["right"].set_visible(False)

    # 设置总标题
    fig.suptitle(
        f"{category_name} 预测结果对比 - 文件 {file_index + 1}",
        fontsize=16,
        fontweight="bold",
        y=0.98,
    )

    # 调整布局 - 纵向排列需要更多垂直间距
    plt.tight_layout()
    plt.subplots_adjust(top=0.93, hspace=0.4)

    # 保存图表
    if save_path:
        save_file = (
            Path(save_path)
            / f"prediction_comparison_file_{file_index + 1}_{chart_suffix}.png"
        )
        save_file.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_file, dpi=300, bbox_inches="tight", facecolor="white")
        logger.info(f"{category_name}图表已保存到: {save_file}")

    # 移除 plt.show() 调用，避免弹出显示窗口
    plt.close(fig)  # 释放内存


def plot_rolling_prediction_comparison(
    actual: np.ndarray,
    predictions: np.ndarray,
    target_columns: List[str],
    rolling_steps: int,
    key_steps: List[int],
    file_index: int = 0,
    save_path: Optional[str] = None,
) -> None:
    """
    绘制滚动预测结果对比图表

    Args:
        actual: 实际值数组，形状为 (n_sequences, rolling_steps, n_targets)
        predictions: 预测值数组，形状为 (n_sequences, rolling_steps, n_targets)
        target_columns: 目标列名列表
        rolling_steps: 滚动预测步数
        key_steps: 关键步数列表
        file_index: 文件索引
        save_path: 保存路径
        max_sequences: 最大显示序列数
    """
    logger = logging.getLogger(__name__)

    try:
        setup_plot_style()

        # 分类列
        categories = categorize_columns(target_columns)

        # 为每个关键步数创建图表
        for step in key_steps:
            if step > rolling_steps:
                continue

            step_idx = step - 1  # 转换为0索引

            # 提取当前步数的数据
            step_actual = actual[:, step_idx, :]
            step_predictions = predictions[:, step_idx, :]

            # 绘制对比图
            step_save_path = None
            if save_path:
                if save_path.endswith(".png"):
                    step_save_path = save_path.replace(".png", f"_step_{step}.png")
                else:
                    step_save_path = f"{save_path}/step_{step}"

            plot_prediction_comparison(
                step_actual,
                step_predictions,
                target_columns,
                file_index=file_index,
                save_path=step_save_path,
                show_confidence=True,
            )

            logger.info(f"已绘制第 {step} 步滚动预测对比图")

    except Exception as e:
        logger.error(f"绘制滚动预测对比图失败: {e}")
        raise


def calculate_and_plot_metrics_summary(
    results: List[Dict], target_columns: List[str], save_path: Optional[str] = None
) -> None:
    """
    计算并绘制多文件预测指标汇总

    Args:
        results: 预测结果列表
        target_columns: 目标列名列表
        save_path: 保存路径
    """
    logger = logging.getLogger(__name__)

    try:
        setup_plot_style()

        if not results:
            logger.warning("没有预测结果可供汇总")
            return

        # 提取指标数据
        metrics_data = []
        for i, result in enumerate(results):
            if "metrics" in result and "per_column" in result["metrics"]:
                for col, metrics in result["metrics"]["per_column"].items():
                    metrics_data.append(
                        {
                            "file_index": i + 1,
                            "column": col,
                            "RMSE": metrics["RMSE"],
                            "MAE": metrics["MAE"],
                            "R2": metrics["R2"],
                        }
                    )

        if not metrics_data:
            logger.warning("没有有效的指标数据")
            return

        df_metrics = pd.DataFrame(metrics_data)

        # 创建指标汇总图
        fig, axes = plt.subplots(2, 2, figsize=(18, 14))

        # RMSE 箱线图
        sns.boxplot(
            data=df_metrics, x="column", y="RMSE", ax=axes[0, 0], palette="Set2"
        )
        axes[0, 0].set_title("RMSE 分布", fontsize=14, fontweight="bold", pad=15)
        axes[0, 0].tick_params(axis="x", rotation=45, labelsize=10)
        axes[0, 0].tick_params(axis="y", labelsize=10)
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].set_xlabel("预测列", fontsize=11)
        axes[0, 0].set_ylabel("RMSE", fontsize=11)

        # MAE 箱线图
        sns.boxplot(data=df_metrics, x="column", y="MAE", ax=axes[0, 1], palette="Set2")
        axes[0, 1].set_title("MAE 分布", fontsize=14, fontweight="bold", pad=15)
        axes[0, 1].tick_params(axis="x", rotation=45, labelsize=10)
        axes[0, 1].tick_params(axis="y", labelsize=10)
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].set_xlabel("预测列", fontsize=11)
        axes[0, 1].set_ylabel("MAE", fontsize=11)

        # R2 箱线图
        sns.boxplot(data=df_metrics, x="column", y="R2", ax=axes[1, 0], palette="Set2")
        axes[1, 0].set_title("R² 分布", fontsize=14, fontweight="bold", pad=15)
        axes[1, 0].tick_params(axis="x", rotation=45, labelsize=10)
        axes[1, 0].tick_params(axis="y", labelsize=10)
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_xlabel("预测列", fontsize=11)
        axes[1, 0].set_ylabel("R²", fontsize=11)

        # 各文件整体指标对比
        overall_metrics = []
        for i, result in enumerate(results):
            if "metrics" in result and "overall" in result["metrics"]:
                overall_metrics.append(
                    {
                        "file_index": i + 1,
                        "RMSE": result["metrics"]["overall"]["RMSE"],
                        "MAE": result["metrics"]["overall"]["MAE"],
                        "R2": result["metrics"]["overall"]["R2"],
                    }
                )

        if overall_metrics:
            df_overall = pd.DataFrame(overall_metrics)
            x_pos = np.arange(len(df_overall))

            # 使用更美观的颜色
            colors = sns.color_palette("Set2", 2)

            axes[1, 1].bar(
                x_pos - 0.2,
                df_overall["RMSE"],
                0.4,
                label="RMSE",
                alpha=0.8,
                color=colors[0],
            )
            axes[1, 1].bar(
                x_pos + 0.2,
                df_overall["MAE"],
                0.4,
                label="MAE",
                alpha=0.8,
                color=colors[1],
            )

            axes[1, 1].set_title(
                "各文件整体指标对比", fontsize=14, fontweight="bold", pad=15
            )
            axes[1, 1].set_xlabel("文件序号", fontsize=11)
            axes[1, 1].set_ylabel("误差值", fontsize=11)
            axes[1, 1].set_xticks(x_pos)
            axes[1, 1].set_xticklabels([f"文件{i}" for i in df_overall["file_index"]])
            axes[1, 1].tick_params(axis="both", labelsize=10)
            axes[1, 1].grid(True, alpha=0.3, axis="y")
            axes[1, 1].legend(fontsize=10)

        plt.suptitle("预测指标汇总分析", fontsize=18, fontweight="bold", y=0.98)
        plt.tight_layout()
        plt.subplots_adjust(top=0.93, hspace=0.3, wspace=0.3)

        # 保存图表
        if save_path:
            save_file = Path(save_path) / "metrics_summary.png"
            save_file.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(save_file, dpi=300, bbox_inches="tight", facecolor="white")
            logger.info(f"指标汇总图已保存到: {save_file}")

        # 移除 plt.show() 调用，避免弹出显示窗口
        plt.close(fig)  # 释放内存

    except Exception as e:
        logger.error(f"绘制指标汇总图失败: {e}")
        raise
