import json
import logging
import glob
import pandas as pd
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List, Tuple
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from config import Configs, Logger
from model.lstm import LitLSTM
from model.gru import LitGRU
from utils.data_utils import normalize_by_params, denormalize
from utils.visualization_utils import (
    plot_prediction_comparison,
    calculate_and_plot_metrics_summary,
    plot_rolling_prediction_comparison,
)

MODEL_DIR = "./data/test-lstm"
USE_ROLLING_PRED = False


class ModelPredictor:
    """模型预测器类，用于加载模型并进行预测"""

    def __init__(self, model_dir: str = MODEL_DIR):
        """
        初始化预测器

        Args:
            model_dir: 模型和元数据所在目录
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.model_dir = Path(model_dir)
        self.meta_dir = self.model_dir / "meta"
        self.data_dir = self.model_dir / "data"

        # 模型相关
        self.model = None
        self.normalize_meta = None

        config = Configs.get_train_config()

        # 数据列定义
        self.input_columns = config.input_columns
        self.target_columns = config.target_columns
        self.non_target_columns = [
            column for column in self.input_columns if column not in self.target_columns
        ]

        # 序列长度配置
        self.seq_length = config.input_len
        # 滚动预测配置
        self.rolling_steps = config.rolling_len
        self.key_steps = (
            [1]
            if self.rolling_steps <= 1
            else [1, self.rolling_steps // 2, self.rolling_steps]
        )

        self.test = 1

    def load_model_and_metadata(self) -> None:
        """加载模型和元数据"""
        try:
            # 加载归一化元数据
            meta_file = self.meta_dir / "normalize_meta.json"
            if not meta_file.exists():
                raise FileNotFoundError(f"元数据文件不存在: {meta_file}")

            with open(meta_file, "r", encoding="utf-8") as f:
                self.normalize_meta = json.load(f)
            self.logger.info(
                f"成功加载归一化元数据，包含 {len(self.normalize_meta)} 个特征"
            )

            # 加载模型
            model_file = self.model_dir / "model.ckpt"
            if not model_file.exists():
                raise FileNotFoundError(f"模型文件不存在: {model_file}")

            # 根据输入输出维度确定模型类型
            input_size = len(self.input_columns)
            output_size = len(self.target_columns)

            # 尝试加载不同类型的模型
            model_classes = [LitLSTM, LitGRU]

            for model_class in model_classes:
                try:
                    self.model = model_class.load_from_checkpoint(
                        str(model_file), input_size=input_size, output_size=output_size
                    )
                    self.model.eval()
                    self.logger.info(f"成功加载模型: {model_class.__name__}")
                    break
                except Exception as e:
                    self.logger.debug(f"尝试加载 {model_class.__name__} 失败: {e}")
                    continue

            if self.model is None:
                raise RuntimeError("无法加载模型，请检查模型文件格式")

        except Exception as e:
            self.logger.error(f"加载模型和元数据失败: {e}")
            raise

    def load_test_data(self) -> List[pd.DataFrame]:
        """加载所有测试数据文件"""
        try:
            test_files = sorted(glob.glob(str(self.data_dir / "data_test_*.csv")))
            if not test_files:
                raise FileNotFoundError(f"在 {self.data_dir} 中未找到测试数据文件")

            test_dataframes = []
            for file_path in test_files:
                df = pd.read_csv(file_path)

                # 检查必要的列是否存在
                missing_cols = [
                    col for col in self.input_columns if col not in df.columns
                ]
                if missing_cols:
                    self.logger.warning(f"文件 {file_path} 缺少列: {missing_cols}")
                    continue

                # 设置时间索引
                if "INDEX" in df.columns:
                    df["INDEX"] = pd.to_datetime(df["INDEX"])
                    df = df.set_index("INDEX")

                # 只保留需要的列
                available_cols = [
                    col for col in self.input_columns if col in df.columns
                ]
                df = df[available_cols]

                # 删除缺失值
                df = df.dropna()

                if (
                    len(df) >= self.seq_length + 1
                ):  # 至少需要seq_length+1行数据才能进行预测
                    test_dataframes.append(df)
                    self.logger.info(
                        f"加载测试文件: {Path(file_path).name}, 数据形状: {df.shape}"
                    )
                else:
                    self.logger.warning(f"文件 {file_path} 数据量不足，跳过")

            if not test_dataframes:
                raise ValueError("没有有效的测试数据文件")

            return test_dataframes

        except Exception as e:
            self.logger.error(f"加载测试数据失败: {e}")
            raise

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """对数据进行预处理（归一化）"""
        try:
            # 检查元数据是否已加载
            if self.normalize_meta is None:
                raise ValueError("归一化元数据未加载")

            # 应用归一化
            df_normalized = normalize_by_params(df, self.normalize_meta)
            return df_normalized
        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            raise

    def create_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """创建时序数据序列"""
        try:
            sequences = []
            targets = []

            for i in range(len(data) - self.seq_length):
                # 输入序列：过去256行的所有特征
                seq = data.iloc[i : i + self.seq_length][self.input_columns].values
                # 目标：第257行的目标列
                target = data.iloc[i + self.seq_length][self.target_columns].values

                sequences.append(seq)
                targets.append(target)

            return np.array(sequences), np.array(targets)

        except Exception as e:
            self.logger.error(f"创建序列数据失败: {e}")
            raise

    def predict_single_file(self, df: pd.DataFrame) -> Dict:
        """对单个数据文件进行预测"""
        try:
            # 检查模型是否已加载
            if self.model is None:
                raise ValueError("模型未加载")

            # 预处理数据
            df_normalized = self.preprocess_data(df)

            # 创建序列
            X, y_true = self.create_sequences(df_normalized)

            if len(X) == 0:
                self.logger.warning("数据量不足，无法创建有效序列")
                return {}

            # 转换为张量
            X_tensor = torch.FloatTensor(X)

            # 模型预测
            with torch.no_grad():
                y_pred = self.model(X_tensor).numpy()

            # 反归一化预测结果和真实值
            y_pred_denorm = self._denormalize_predictions(y_pred)
            y_true_denorm = self._denormalize_predictions(y_true)

            # 计算评估指标
            metrics = self._calculate_metrics(y_true_denorm, y_pred_denorm)

            return {
                "predictions": y_pred_denorm,
                "actual": y_true_denorm,
                "metrics": metrics,
                "num_samples": len(X),
            }

        except Exception as e:
            self.logger.error(f"单文件预测失败: {e}")
            raise

    def _denormalize_predictions(self, predictions: np.ndarray) -> np.ndarray:
        """反归一化预测结果"""
        try:
            # 检查元数据是否已加载
            if self.normalize_meta is None:
                self.logger.warning("归一化元数据未加载，返回原始预测结果")
                return predictions

            # 创建临时DataFrame进行反归一化
            pred_df = pd.DataFrame(predictions, columns=self.target_columns)

            # 只对目标列进行反归一化
            target_meta = {
                col: self.normalize_meta[col]
                for col in self.target_columns
                if col in self.normalize_meta
            }

            if not target_meta:
                self.logger.warning("没有找到目标列的归一化参数，返回原始预测结果")
                return predictions

            denorm_df = denormalize(pred_df, target_meta)
            return denorm_df.values

        except Exception as e:
            self.logger.error(f"反归一化失败: {e}")
            return predictions

    def rolling_predict_single_file(self, df: pd.DataFrame) -> Dict:
        """对单个数据文件进行滚动预测"""
        try:
            # 检查模型是否已加载
            if self.model is None:
                raise ValueError("模型未加载")

            # 预处理数据
            df_normalized = self.preprocess_data(df)

            # 检查数据长度是否足够进行滚动预测
            min_required_length = self.seq_length + self.rolling_steps
            if len(df_normalized) < min_required_length:
                self.logger.warning(
                    f"数据长度不足，需要至少{min_required_length}行，实际{len(df_normalized)}行"
                )
                return {}

            # 存储所有滚动预测结果
            all_predictions = []
            all_actuals = []
            step_metrics = {}

            # 计算可以进行滚动预测的起始位置数量
            num_sequences = (
                len(df_normalized) - self.seq_length - self.rolling_steps + 1
            )

            # 为了演示和测试，限制序列数量（可以根据需要调整）
            max_sequences = min(num_sequences, 1000)  # 限制为100个序列进行演示

            self.logger.info(
                f"开始滚动预测，共{max_sequences}个序列（总共{num_sequences}个可用），每个序列预测{self.rolling_steps}步"
            )

            for seq_idx in range(max_sequences):
                # 添加进度显示
                if seq_idx % 20 == 0 or seq_idx == max_sequences - 1:
                    progress = (seq_idx + 1) / max_sequences * 100
                    self.logger.info(
                        f"  进度: {seq_idx + 1}/{max_sequences} ({progress:.1f}%)"
                    )
                # 获取初始序列（256步）
                start_idx = seq_idx
                end_idx = start_idx + self.seq_length

                # 初始输入序列
                current_sequence = df_normalized.iloc[start_idx:end_idx][
                    self.input_columns
                ].values.copy()

                # 存储当前序列的预测结果
                sequence_predictions = []
                sequence_actuals = []

                # 滚动预测32步
                for step in range(self.rolling_steps):
                    # 当前真实值的索引
                    actual_idx = end_idx + step

                    # 获取真实值
                    actual_values = df_normalized.iloc[actual_idx][
                        self.target_columns
                    ].values
                    sequence_actuals.append(actual_values)

                    # 准备模型输入（添加batch维度）
                    model_input = torch.FloatTensor(current_sequence).unsqueeze(0)

                    # 模型预测
                    with torch.no_grad():
                        pred_output = self.model(model_input).numpy()[
                            0
                        ]  # 移除batch维度

                    sequence_predictions.append(pred_output)

                    # 准备下一步的输入序列
                    if step < self.rolling_steps - 1:  # 不是最后一步
                        # 获取下一步的非目标列实际值
                        next_actual_idx = actual_idx
                        next_non_target_values = df_normalized.iloc[next_actual_idx][
                            self.non_target_columns
                        ].values

                        # 组合预测的目标列和实际的非目标列
                        next_step_features = np.zeros(len(self.input_columns))

                        # 填入非目标列的实际值
                        for i, col in enumerate(self.input_columns):
                            if col in self.non_target_columns:
                                non_target_idx = self.non_target_columns.index(col)
                                next_step_features[i] = next_non_target_values[
                                    non_target_idx
                                ]
                            else:  # 目标列使用预测值
                                target_idx = self.target_columns.index(col)
                                next_step_features[i] = pred_output[target_idx]

                        # 滑动窗口：移除最早的一步，添加新的一步
                        current_sequence = np.vstack(
                            [current_sequence[1:], next_step_features.reshape(1, -1)]
                        )

                # 存储当前序列的结果
                all_predictions.append(np.array(sequence_predictions))
                all_actuals.append(np.array(sequence_actuals))

            # 转换为numpy数组 [num_sequences, rolling_steps, num_targets]
            all_predictions = np.array(all_predictions)
            all_actuals = np.array(all_actuals)

            # 反归一化
            all_predictions_denorm = self._denormalize_rolling_predictions(
                all_predictions
            )
            all_actuals_denorm = self._denormalize_rolling_predictions(all_actuals)

            # 计算每一步的指标
            for step in range(self.rolling_steps):
                step_preds = all_predictions_denorm[
                    :, step, :
                ]  # [num_sequences, num_targets]
                step_actuals = all_actuals_denorm[:, step, :]

                step_metrics[step + 1] = self._calculate_metrics(
                    step_actuals, step_preds
                )

            return {
                "predictions": all_predictions_denorm,
                "actual": all_actuals_denorm,
                "step_metrics": step_metrics,
                "num_sequences": max_sequences,  # 使用实际处理的序列数量
                "rolling_steps": self.rolling_steps,
            }

        except Exception as e:
            self.logger.error(f"滚动预测失败: {e}")
            raise

    def _denormalize_rolling_predictions(self, predictions: np.ndarray) -> np.ndarray:
        """反归一化滚动预测结果

        Args:
            predictions: 形状为 [num_sequences, rolling_steps, num_targets] 的预测结果

        Returns:
            反归一化后的预测结果，形状相同
        """
        try:
            # 检查元数据是否已加载
            if self.normalize_meta is None:
                self.logger.warning("归一化元数据未加载，返回原始预测结果")
                return predictions

            num_sequences, rolling_steps, num_targets = predictions.shape
            denormalized = np.zeros_like(predictions)

            # 只对目标列进行反归一化
            target_meta = {
                col: self.normalize_meta[col]
                for col in self.target_columns
                if col in self.normalize_meta
            }

            if not target_meta:
                self.logger.warning("没有找到目标列的归一化参数，返回原始预测结果")
                return predictions

            # 对每个序列的每一步进行反归一化
            for seq_idx in range(num_sequences):
                for step_idx in range(rolling_steps):
                    # 创建临时DataFrame进行反归一化
                    step_data = predictions[seq_idx, step_idx, :].reshape(1, -1)
                    pred_df = pd.DataFrame(step_data, columns=self.target_columns)

                    denorm_df = denormalize(pred_df, target_meta)
                    denormalized[seq_idx, step_idx, :] = denorm_df.values[0]

            return denormalized

        except Exception as e:
            self.logger.error(f"滚动预测反归一化失败: {e}")
            return predictions

    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """计算评估指标"""
        try:
            metrics = {}

            # 整体指标
            mse = mean_squared_error(y_true, y_pred)
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_true, y_pred)

            metrics["overall"] = {
                "MSE": float(mse),
                "MAE": float(mae),
                "RMSE": float(rmse),
                "R2": float(r2),
            }

            # 每个目标列的指标
            metrics["per_column"] = {}
            for i, col in enumerate(self.target_columns):
                col_mse = mean_squared_error(y_true[:, i], y_pred[:, i])
                col_mae = mean_absolute_error(y_true[:, i], y_pred[:, i])
                col_rmse = np.sqrt(col_mse)
                col_r2 = r2_score(y_true[:, i], y_pred[:, i])

                metrics["per_column"][col] = {
                    "MSE": float(col_mse),
                    "MAE": float(col_mae),
                    "RMSE": float(col_rmse),
                    "R2": float(col_r2),
                }

            return metrics

        except Exception as e:
            self.logger.error(f"计算指标失败: {e}")
            return {}


def main(use_rolling_prediction: bool = USE_ROLLING_PRED):
    """主函数：执行模型预测

    Args:
        use_rolling_prediction: 是否使用滚动预测模式，默认为True
    """
    try:
        # 初始化配置和日志
        Configs.initialize()
        Logger.initialize()

        logger = logging.getLogger(__name__)

        if use_rolling_prediction:
            logger.info("开始执行LSTM滚动预测")
            return main_rolling_prediction()
        else:
            logger.info("开始执行标准模型预测")
            return main_standard_prediction()

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"预测过程发生错误: {e}")
        raise


def main_rolling_prediction():
    """滚动预测主函数"""
    logger = logging.getLogger(__name__)

    # 创建预测器
    predictor = ModelPredictor(model_dir="./data/test-gru")

    # 加载模型和元数据
    logger.info("加载模型和元数据...")
    predictor.load_model_and_metadata()

    # 加载测试数据
    logger.info("加载测试数据...")
    test_dataframes = predictor.load_test_data()

    # 对每个测试文件进行滚动预测
    all_rolling_results = []

    for i, df in enumerate(test_dataframes):
        logger.info(f"处理测试文件 {i+1}/{len(test_dataframes)} - 滚动预测模式")

        result = predictor.rolling_predict_single_file(df)
        if result:
            all_rolling_results.append(result)

            # 记录单文件滚动预测结果
            logger.info(f"文件 {i+1} 滚动预测完成:")
            logger.info(f"  序列数量: {result['num_sequences']}")
            logger.info(f"  预测步数: {result['rolling_steps']}")

            # 记录重点步数的指标
            for step in predictor.key_steps:
                if step in result["step_metrics"]:
                    metrics = result["step_metrics"][step]["overall"]
                    logger.info(
                        f"  第{step}步 - RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}, R2: {metrics['R2']:.4f}"
                    )

            # 绘制滚动预测对比图
            try:
                logger.info(f"正在生成文件 {i+1} 的滚动预测对比图...")
                visualization_path = str(
                    predictor.model_dir / "visualizations" / "rolling"
                )
                plot_rolling_prediction_comparison(
                    actual=result["actual"],
                    predictions=result["predictions"],
                    target_columns=predictor.target_columns,
                    rolling_steps=result["rolling_steps"],
                    key_steps=predictor.key_steps,
                    file_index=i,
                    save_path=visualization_path,
                )
                logger.info(f"文件 {i+1} 的滚动预测对比图生成完成")
            except Exception as e:
                logger.warning(f"生成文件 {i+1} 的滚动预测对比图失败: {e}")

    # 计算总体滚动预测指标
    if all_rolling_results:
        logger.info("=" * 60)
        logger.info("总体滚动预测结果分析:")

        # 合并所有文件的结果
        combined_predictions = np.concatenate(
            [r["predictions"] for r in all_rolling_results], axis=0
        )
        combined_actuals = np.concatenate(
            [r["actual"] for r in all_rolling_results], axis=0
        )

        total_sequences = sum(r["num_sequences"] for r in all_rolling_results)
        logger.info(f"总序列数: {total_sequences}")

        # 计算每一步的总体指标
        for step in range(1, predictor.rolling_steps + 1):
            step_preds = combined_predictions[:, step - 1, :]
            step_actuals = combined_actuals[:, step - 1, :]

            step_metrics = predictor._calculate_metrics(step_actuals, step_preds)

            if step in predictor.key_steps:
                logger.info(f"\n第{step}步预测指标:")
                logger.info(f"  整体RMSE: {step_metrics['overall']['RMSE']:.4f}")
                logger.info(f"  整体MAE: {step_metrics['overall']['MAE']:.4f}")
                logger.info(f"  整体R2: {step_metrics['overall']['R2']:.4f}")

                # 显示每列的详细指标
                logger.info(f"  各列详细指标:")
                for col, metrics in step_metrics["per_column"].items():
                    logger.info(
                        f"    {col:15} - RMSE: {metrics['RMSE']:8.4f}, MAE: {metrics['MAE']:8.4f}, R2: {metrics['R2']:8.4f}"
                    )

        # 分析误差趋势
        logger.info("\n预测误差趋势分析:")
        rmse_trend = []
        mae_trend = []
        r2_trend = []

        for step in range(1, predictor.rolling_steps + 1):
            step_preds = combined_predictions[:, step - 1, :]
            step_actuals = combined_actuals[:, step - 1, :]
            step_metrics = predictor._calculate_metrics(step_actuals, step_preds)

            rmse_trend.append(step_metrics["overall"]["RMSE"])
            mae_trend.append(step_metrics["overall"]["MAE"])
            r2_trend.append(step_metrics["overall"]["R2"])

        logger.info(
            f"RMSE变化: 第1步={rmse_trend[0]:.4f} → 第32步={rmse_trend[-1]:.4f} (变化率: {((rmse_trend[-1]/rmse_trend[0])-1)*100:+.2f}%)"
        )
        logger.info(
            f"MAE变化:  第1步={mae_trend[0]:.4f} → 第32步={mae_trend[-1]:.4f} (变化率: {((mae_trend[-1]/mae_trend[0])-1)*100:+.2f}%)"
        )
        logger.info(
            f"R2变化:   第1步={r2_trend[0]:.4f} → 第32步={r2_trend[-1]:.4f} (变化: {r2_trend[-1]-r2_trend[0]:+.4f})"
        )

        logger.info("滚动预测完成！")
    else:
        logger.warning("没有成功的滚动预测结果")


def main_standard_prediction():
    """标准预测主函数（原有功能）"""
    logger = logging.getLogger(__name__)

    # 创建预测器
    predictor = ModelPredictor()

    # 加载模型和元数据
    logger.info("加载模型和元数据...")
    predictor.load_model_and_metadata()

    # 加载测试数据
    logger.info("加载测试数据...")
    test_dataframes = predictor.load_test_data()

    # 对每个测试文件进行预测
    all_results = []
    overall_metrics = {"predictions": [], "actual": []}

    for i, df in enumerate(test_dataframes):
        logger.info(f"处理测试文件 {i+1}/{len(test_dataframes)}")

        result = predictor.predict_single_file(df)
        if result:
            all_results.append(result)
            overall_metrics["predictions"].append(result["predictions"])
            overall_metrics["actual"].append(result["actual"])

            # 记录单文件结果
            logger.info(f"文件 {i+1} 预测完成:")
            logger.info(f"  样本数量: {result['num_samples']}")
            logger.info(f"  整体RMSE: {result['metrics']['overall']['RMSE']:.4f}")
            logger.info(f"  整体MAE: {result['metrics']['overall']['MAE']:.4f}")
            logger.info(f"  整体R2: {result['metrics']['overall']['R2']:.4f}")

            # 记录每列的详细指标
            for col, metrics in result["metrics"]["per_column"].items():
                logger.info(
                    f"  {col} - RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}, R2: {metrics['R2']:.4f}"
                )

            # 绘制单文件预测对比图
            try:
                logger.info(f"正在生成文件 {i+1} 的预测对比图...")
                visualization_path = str(predictor.model_dir / "visualizations")
                plot_prediction_comparison(
                    actual=result["actual"],
                    predictions=result["predictions"],
                    target_columns=predictor.target_columns,
                    file_index=i,
                    save_path=visualization_path,
                    show_confidence=True,
                )
                logger.info(f"文件 {i+1} 的预测对比图生成完成")
            except Exception as e:
                logger.warning(f"生成文件 {i+1} 的预测对比图失败: {e}")

    # 计算总体指标
    if overall_metrics["predictions"]:
        all_predictions = np.vstack(overall_metrics["predictions"])
        all_actual = np.vstack(overall_metrics["actual"])

        final_metrics = predictor._calculate_metrics(all_actual, all_predictions)

        logger.info("=" * 50)
        logger.info("总体预测结果:")
        logger.info(f"总样本数: {len(all_predictions)}")
        logger.info(f"整体RMSE: {final_metrics['overall']['RMSE']:.4f}")
        logger.info(f"整体MAE: {final_metrics['overall']['MAE']:.4f}")
        logger.info(f"整体R2: {final_metrics['overall']['R2']:.4f}")

        logger.info("各列详细指标:")
        for col, metrics in final_metrics["per_column"].items():
            logger.info(
                f"{col:15} - RMSE: {metrics['RMSE']:8.4f}, MAE: {metrics['MAE']:8.4f}, R2: {metrics['R2']:8.4f}"
            )

        # 生成指标汇总图
        try:
            logger.info("正在生成预测指标汇总图...")
            visualization_path = str(predictor.model_dir / "visualizations")
            calculate_and_plot_metrics_summary(
                results=all_results,
                target_columns=predictor.target_columns,
                save_path=visualization_path,
            )
            logger.info("预测指标汇总图生成完成")
        except Exception as e:
            logger.warning(f"生成预测指标汇总图失败: {e}")

        logger.info("模型预测完成！")
    else:
        logger.warning("没有成功的预测结果")


if __name__ == "__main__":
    main()
