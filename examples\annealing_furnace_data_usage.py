#!/usr/bin/env python3
"""
退火炉数据加载器使用示例
"""

import sys
import os
from datetime import datetime, timedelta
import pandas as pd

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.influxql_helper import InfluxQuery


class AnnealingFurnaceDataLoader:
    """
    退火炉数据加载器示例类
    演示如何使用influxql_helper读取_MEASUREMENTS中的数据
    """
    
    # 退火炉测量点映射
    _MEASUREMENTS = {
        "X2_01.PLC01.DB844,DBX1056.0": "SELECTED",
        "X2_01.PLC01.DB844,STRING0": "STRIP_NAME_1",
        "X2_01.PLC01.DB844,STRING256": "STRIP_TYPE_1",
        "X2_01.PLC01.DB844,REAL512": "STRIP_LENGTH_1",
        "X2_01.PLC01.DB844,REAL516": "STRIP_WIDTH_1",
        "X2_01.PLC01.DB844,REAL520": "STRIP_THICK_1",
        "X2_01.PLC01.DB844,REAL524": "STRIP_WEIGHT_1",
        "X2_01.PLC01.DB844,STRING528": "STRIP_NAME_2",
        "X2_01.PLC01.DB844,STRING784": "STRIP_TYPE_2",
        "X2_01.PLC01.DB844,REAL1040": "STRIP_LENGTH_2",
        "X2_01.PLC01.DB844,REAL1044": "STRIP_WIDTH_2",
        "X2_01.PLC01.DB844,REAL1048": "STRIP_THICK_2",
        "X2_01.PLC01.DB844,REAL1052": "STRIP_WEIGHT_2",
        "X2_01.PLC02.DB844,REAL2": "WELD_POSITION_1",
        "X2_01.PLC02.DB844,REAL6": "WELD_POSITION_2",
        "X2_01.PLC02.DB844,INT0": "SPEED",
        "Luzi.PLC03.DB34,REAL106": "TEMP_PH",
        "Luzi.PLC03.DB34,REAL110": "TEMP_NOF1",
        "Luzi.PLC03.DB34,REAL114": "TEMP_NOF2",
        "Luzi.PLC03.DB34,REAL118": "TEMP_NOF3",
        "Luzi.PLC03.DB34,REAL122": "TEMP_NOF4",
        "Luzi.PLC03.DB34,REAL126": "TEMP_NOF5",
        "Luzi.PLC03.DB34,REAL130": "TEMP_RTF1",
        "Luzi.PLC03.DB34,REAL134": "TEMP_RTF2",
        "Luzi.PLC03.DB34,REAL138": "TEMP_SF",
        "Luzi.PLC03.DB34,REAL142": "TEMP_JCF1",
        "Luzi.PLC03.DB34,REAL146": "TEMP_JCF2",
        "Luzi.PLC03.DB34,REAL150": "TEMP_JCF3",
        "Luzi.PLC03.DB34,REAL154": "TEMP_JCF4",
        "Luzi.PLC03.DB34,REAL158": "TEMP_LTH",
        "Luzi.PLC03.DB34,REAL162": "TEMP_TDS",
        "Luzi.PLC03.DB34,REAL166": "STRIP_TEMP_NOF",
        "Luzi.PLC03.DB34,REAL170": "STRIP_TEMP_RTF",
        "Luzi.PLC03.DB34,REAL174": "STRIP_TEMP_SF",
        "Luzi.PLC03.DB34,REAL178": "STRIP_TEMP_JCF",
        "Luzi.PLC03.DB34,REAL182": "STRIP_TEMP_LTH",
        "Luzi.PLC03.DB34,REAL186": "AFR_NOF1",
        "Luzi.PLC03.DB34,REAL190": "AFR_NOF2",
        "Luzi.PLC03.DB34,REAL194": "AFR_NOF3",
        "Luzi.PLC03.DB34,REAL198": "AFR_NOF4",
        "Luzi.PLC03.DB34,REAL202": "AFR_NOF5",
        "Luzi.PLC03.DB34,REAL206": "AFR_RTF1",
        "Luzi.PLC03.DB34,REAL210": "AFR_RTF2",
        "Luzi.PLC03.DB34,REAL214": "AFR_SF",
        "Luzi.PLC03.DB34,REAL218": "HEAT_LOAD_NOF1",
        "Luzi.PLC03.DB34,REAL222": "HEAT_LOAD_NOF2",
        "Luzi.PLC03.DB34,REAL226": "HEAT_LOAD_NOF3",
        "Luzi.PLC03.DB34,REAL230": "HEAT_LOAD_NOF4",
        "Luzi.PLC03.DB34,REAL234": "HEAT_LOAD_NOF5",
        "Luzi.PLC03.DB34,REAL238": "HEAT_LOAD_RTF1",
        "Luzi.PLC03.DB34,REAL242": "HEAT_LOAD_RTF2",
        "Luzi.PLC03.DB34,REAL246": "HEAT_LOAD_SF",
        "Luzi.PLC03.DB34,REAL250": "GAS_FLOW_TOTAL",
        "Luzi.PLC03.DB34,REAL254": "GAS_PRESSURE_NOF",
        "Luzi.PLC03.DB34,REAL258": "GAS_FLOW_NOF1",
        "Luzi.PLC03.DB34,REAL262": "GAS_FLOW_NOF2",
        "Luzi.PLC03.DB34,REAL266": "GAS_FLOW_NOF3",
        "Luzi.PLC03.DB34,REAL270": "GAS_FLOW_NOF4",
        "Luzi.PLC03.DB34,REAL274": "GAS_FLOW_NOF5",
        "Luzi.PLC03.DB34,REAL278": "GAS_FLOW_RTF1",
        "Luzi.PLC03.DB34,REAL282": "GAS_FLOW_RTF2",
        "Luzi.PLC03.DB34,REAL286": "GAS_FLOW_SF",
        "Luzi.PLC03.DB34,REAL290": "AIR_FLOW_NOF1",
        "Luzi.PLC03.DB34,REAL294": "AIR_FLOW_NOF2",
        "Luzi.PLC03.DB34,REAL298": "AIR_FLOW_NOF3",
        "Luzi.PLC03.DB34,REAL302": "AIR_FLOW_NOF4",
        "Luzi.PLC03.DB34,REAL306": "AIR_FLOW_NOF5",
        "Luzi.PLC03.DB34,REAL310": "AIR_FLOW_RTF1",
        "Luzi.PLC03.DB34,REAL314": "AIR_FLOW_RTF2",
        "Luzi.PLC03.DB34,REAL318": "AIR_FLOW_SF",
        "Luzi.PLC03.DB34,REAL322": "AIR_FLOW_PH",
    }
    
    def __init__(self, host="***********", port=8086, username="root", 
                 password="123456", database="annealing_furnace"):
        """
        初始化数据加载器
        
        Args:
            host: InfluxDB主机地址
            port: InfluxDB端口
            username: 用户名
            password: 密码
            database: 数据库名称
        """
        self.influx_query = InfluxQuery(host, port, username, password, database)
    
    def prepare_data(self, start_time: datetime, end_time: datetime, 
                    freq: str = "1min", save_to_file: bool = False,
                    output_dir: str = "data") -> pd.DataFrame:
        """
        从InfluxDB读取_MEASUREMENTS中的数据并返回pandas.DataFrame
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            freq: 数据采样频率，如"1min", "1s"
            save_to_file: 是否保存到文件
            output_dir: 输出目录
            
        Returns:
            pd.DataFrame: 包含所有测量点数据的DataFrame，索引为时间戳
        """
        try:
            print(f"开始从InfluxDB读取退火炉数据...")
            print(f"时间范围: {start_time} 到 {end_time}")
            print(f"采样频率: {freq}")
            
            # 获取所有测量点名称
            measurements = list(self._MEASUREMENTS.keys())
            print(f"准备查询 {len(measurements)} 个测量点")
            
            # 转换时间格式为ISO8601
            start_time_iso = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            end_time_iso = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            
            # 分批查询（避免一次查询过多测量点）
            batch_size = 15  # 每批查询15个测量点
            all_data = []
            
            for i in range(0, len(measurements), batch_size):
                batch_measurements = measurements[i:i+batch_size]
                print(f"查询批次 {i//batch_size + 1}/{(len(measurements)-1)//batch_size + 1}: {len(batch_measurements)} 个测量点")
                
                try:
                    # 使用batch_query_aligned_df进行批量查询
                    df_batch = self.influx_query.batch_query_aligned_df(
                        measurements=batch_measurements,
                        field="value",  # 可以改为多字段查询
                        start_time=start_time_iso,
                        end_time=end_time_iso,
                        time_interval=freq,
                        fill_method="linear",
                        resample=True
                    )
                    
                    if df_batch is not None and not df_batch.empty:
                        all_data.append(df_batch)
                        print(f"   批次 {i//batch_size + 1} 成功: {df_batch.shape}")
                    else:
                        print(f"   批次 {i//batch_size + 1} 无数据")
                        
                except Exception as e:
                    print(f"   批次 {i//batch_size + 1} 失败: {e}")
            
            # 合并所有数据
            if all_data:
                df = pd.concat(all_data, axis=1)
                print(f"合并后的数据形状: {df.shape}")
            else:
                print("没有获取到任何数据")
                return pd.DataFrame()
            
            # 重命名列名为更友好的名称
            column_mapping = {}
            for measurement, friendly_name in self._MEASUREMENTS.items():
                if measurement in df.columns:
                    column_mapping[measurement] = friendly_name
            
            if column_mapping:
                df = df.rename(columns=column_mapping)
                print(f"重命名了 {len(column_mapping)} 个列名")
            
            # 保存数据到文件（如果需要）
            if save_to_file:
                self._save_data_to_file(df, start_time, end_time, output_dir)
            
            print("数据准备完成")
            return df
            
        except Exception as e:
            print(f"从InfluxDB读取数据失败: {e}")
            raise e
    
    def _save_data_to_file(self, df: pd.DataFrame, start_time: datetime, 
                          end_time: datetime, output_dir: str):
        """保存数据到文件"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            start_str = start_time.strftime("%Y%m%d_%H%M%S")
            end_str = end_time.strftime("%Y%m%d_%H%M%S")
            
            # 保存为CSV
            csv_filename = f"annealing_furnace_data_{start_str}_to_{end_str}.csv"
            csv_filepath = os.path.join(output_dir, csv_filename)
            df.to_csv(csv_filepath, index=True)
            print(f"数据已保存到: {csv_filepath}")
            
            # 保存为Parquet（更高效）
            parquet_filename = f"annealing_furnace_data_{start_str}_to_{end_str}.parquet"
            parquet_filepath = os.path.join(output_dir, parquet_filename)
            df.to_parquet(parquet_filepath, index=True)
            print(f"数据已保存到: {parquet_filepath}")
            
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def get_temperature_data(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """获取温度相关数据"""
        temp_measurements = {k: v for k, v in self._MEASUREMENTS.items() if 'TEMP' in v}
        
        try:
            measurements = list(temp_measurements.keys())
            start_time_iso = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            end_time_iso = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            
            df = self.influx_query.batch_query_aligned_df(
                measurements=measurements,
                field="value",
                start_time=start_time_iso,
                end_time=end_time_iso,
                time_interval="1min",
                fill_method="linear",
                resample=True
            )
            
            if df is not None and not df.empty:
                # 重命名列名
                column_mapping = {k: v for k, v in temp_measurements.items() if k in df.columns}
                if column_mapping:
                    df = df.rename(columns=column_mapping)
                
            return df if df is not None else pd.DataFrame()
            
        except Exception as e:
            print(f"获取温度数据失败: {e}")
            return pd.DataFrame()
    
    def close(self):
        """关闭数据库连接"""
        self.influx_query.close()


def example_usage():
    """使用示例"""
    print("=== 退火炉数据加载器使用示例 ===")
    
    # 创建数据加载器
    loader = AnnealingFurnaceDataLoader()
    
    try:
        # 示例1: 获取最近1小时的数据
        print("\n示例1: 获取最近1小时的数据")
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        df = loader.prepare_data(
            start_time=start_time,
            end_time=end_time,
            freq="1min",
            save_to_file=True,
            output_dir="example_output"
        )
        
        if not df.empty:
            print(f"获取到数据: {df.shape}")
            print(f"列名示例: {df.columns[:5].tolist()}")
        
        # 示例2: 获取特定时间范围的温度数据
        print("\n示例2: 获取特定时间范围的温度数据")
        start_time = datetime(2025, 4, 16, 0, 29, 0)
        end_time = datetime(2025, 4, 16, 0, 30, 0)
        
        temp_df = loader.get_temperature_data(start_time, end_time)
        
        if not temp_df.empty:
            print(f"温度数据: {temp_df.shape}")
            print(f"温度列名: {temp_df.columns.tolist()}")
            print("\n温度数据前5行:")
            print(temp_df.head())
        
    finally:
        # 关闭连接
        loader.close()


if __name__ == "__main__":
    example_usage()
