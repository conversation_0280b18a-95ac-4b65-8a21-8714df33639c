import enum


class FileNameEnum(enum.Enum):
    TRACK_HIS = "data_track_history"
    TRACKED = "data_tracked"
    SOURCE = "data_source"
    SORTED = "data_sorted"
    INDEXED = "data_indexed"
    ANOMALY_REMOVED = "data_anomaly_removed"
    FILLED = "data_filled"
    RESAMPLED = "data_resampled"
    FINAL = "data_final"
    SEGMENT = "data_segment"
    TRAIN = "data_train"
    VALID = "data_valid"
    TEST = "data_test"
    NORMALIZE_META = "normalize_meta"
    STEEL_GRADE_META = "steel_grade_meta"
    FTM_NAME = "furnace_temp_model"
    STM_NAME = "strip_temp_model"
