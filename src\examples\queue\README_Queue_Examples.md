# Python Queue进程间通信示例

本项目包含了使用Python `multiprocessing.Queue` 进行进程间通信的完整示例。

## 文件说明

### 1. `queue_communication_example.py` - 基础示例
这是一个简单的Queue通信示例，包含：
- **发送进程**：定期生成消息并发送到队列
- **接收进程**：从队列接收消息并进行处理

**特点：**
- 一对一通信模式
- 包含消息结构设计
- 异常处理和超时机制
- 优雅的进程终止

### 2. `advanced_queue_example.py` - 高级示例
这是一个更复杂的多进程通信示例，包含：
- **多个生产者进程**：并发生成任务
- **多个消费者进程**：并发处理任务
- **结果收集进程**：收集和统计处理结果

**特点：**
- 多生产者-多消费者模式
- 任务状态管理
- 结果统计和持久化
- 负载均衡处理

### 3. `run_examples.py` - 运行脚本
提供交互式界面来运行示例程序。

## 运行方法

### 方法一：直接运行单个示例

```bash
# 运行基础示例
python queue_communication_example.py

# 运行高级示例
python advanced_queue_example.py
```

### 方法二：使用运行脚本

```bash
python run_examples.py
```

然后根据提示选择要运行的示例。

## 核心概念

### Queue的主要方法

- `queue.put(item)` - 向队列添加项目
- `queue.get()` - 从队列获取项目（阻塞）
- `queue.get(timeout=5)` - 带超时的获取
- `queue.empty()` - 检查队列是否为空
- `queue.qsize()` - 获取队列大小

### 进程间通信流程

1. **创建队列**：`multiprocessing.Queue()`
2. **启动进程**：将队列作为参数传递给进程
3. **发送消息**：使用 `queue.put()` 发送
4. **接收消息**：使用 `queue.get()` 接收
5. **优雅退出**：发送特殊的结束信号

## 示例输出

### 基础示例输出
```
=== 进程间Queue通信示例 ===
启动发送进程...
启动接收进程...
[发送者] 发送进程启动
[接收者] 接收进程启动
[发送者] 发送消息: 来自发送者的消息 #1, 值: 42
[接收者] 接收到消息: 来自发送者的消息 #1
[接收者] 处理完成: 原值=42, 处理后=84, 处理时间=1.23秒
...
```

### 高级示例输出
```
=== 高级Queue通信示例：多生产者-多消费者模式 ===
启动 2 个生产者进程...
启动 3 个消费者进程...
[生产者-1] 生成任务: P1-T1, 操作: add
[消费者-1] 开始处理任务: P1-T1
[消费者-1] 任务 P1-T1 处理完成: [25, 67] add = 92
...
```

## 关键特性

### 1. 线程安全
`multiprocessing.Queue` 是线程安全的，多个进程可以同时读写。

### 2. 阻塞和非阻塞操作
- `get()` - 阻塞直到有数据
- `get(timeout=5)` - 最多等待5秒
- `get_nowait()` - 立即返回或抛出异常

### 3. 队列大小限制
```python
queue = multiprocessing.Queue(maxsize=10)  # 最多10个项目
```

### 4. 异常处理
```python
try:
    item = queue.get(timeout=5)
except multiprocessing.TimeoutError:
    print("获取超时")
except Exception as e:
    print(f"其他错误: {e}")
```

## 最佳实践

### 1. 消息结构设计
使用字典结构包含元数据：
```python
message = {
    'id': 1,
    'timestamp': datetime.now().isoformat(),
    'type': 'data',
    'payload': {...}
}
```

### 2. 优雅退出
发送特殊的结束信号：
```python
# 发送结束信号
queue.put(None)

# 接收端检查
if message is None:
    break
```

### 3. 错误处理
- 使用超时避免无限等待
- 捕获和记录异常
- 实现重试机制

### 4. 性能优化
- 合理设置队列大小
- 避免发送大对象
- 考虑使用批处理

## 注意事项

### Windows平台
在Windows上运行多进程程序时，需要添加：
```python
if __name__ == "__main__":
    multiprocessing.freeze_support()
    main()
```

### 内存管理
- Queue中的对象会被序列化，大对象会影响性能
- 及时清理已处理的消息
- 监控队列大小避免内存泄漏

### 进程生命周期
- 使用 `process.join()` 等待进程完成
- 使用 `process.terminate()` 强制终止
- 处理 `KeyboardInterrupt` 实现优雅退出

## 扩展应用

这些示例可以扩展用于：
- 任务调度系统
- 数据处理管道
- 微服务间通信
- 批处理作业
- 实时数据流处理

## 相关技术

- `multiprocessing.Pipe()` - 双向通信管道
- `multiprocessing.Manager()` - 共享对象管理
- `concurrent.futures` - 高级并发接口
- `asyncio.Queue` - 异步队列 