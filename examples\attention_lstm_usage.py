#!/usr/bin/env python3
"""
注意力增强LSTM模型使用示例

本示例展示如何使用新实现的注意力增强LSTM模型进行时间序列预测。
包括模型创建、训练和推理的完整流程。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import lightning as L
from torch.utils.data import DataLoader
from lightning.pytorch.callbacks import ModelCheckpoint, EarlyStopping

from src.model.lstm import (
    LitLSTMWithAttention,
    LitLSTMWithAttentionMultiStep,
    LitLSTM,  # 用于对比
)
from src.dataset.time_series_dataset import TimeSeriesDataset


def create_synthetic_data(n_samples=1000, seq_length=50, n_features=5):
    """
    创建合成时间序列数据用于演示
    
    Args:
        n_samples: 样本数量
        seq_length: 序列长度
        n_features: 特征数量
    
    Returns:
        DataFrame: 合成的时间序列数据
    """
    np.random.seed(42)
    
    # 生成时间索引
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='1H')
    
    # 生成合成数据（包含趋势、季节性和噪声）
    t = np.arange(n_samples)
    
    data = {}
    for i in range(n_features):
        # 趋势分量
        trend = 0.01 * t + np.sin(2 * np.pi * t / 100) * 10
        # 季节性分量
        seasonal = np.sin(2 * np.pi * t / 24) * 5 + np.cos(2 * np.pi * t / 168) * 3
        # 噪声
        noise = np.random.normal(0, 2, n_samples)
        
        data[f'feature_{i}'] = trend + seasonal + noise
    
    # 目标变量（基于特征的组合）
    data['target'] = (
        0.3 * data['feature_0'] + 
        0.2 * data['feature_1'] + 
        0.1 * data['feature_2'] + 
        np.random.normal(0, 1, n_samples)
    )
    
    df = pd.DataFrame(data, index=dates)
    return df


def prepare_data(df, seq_length=20, train_ratio=0.7, val_ratio=0.15):
    """
    准备训练、验证和测试数据

    Args:
        df: 输入数据框
        seq_length: 序列长度
        train_ratio: 训练集比例
        val_ratio: 验证集比例

    Returns:
        tuple: (train_dataset, val_dataset, test_dataset, input_cols, output_cols)
    """
    # 分割训练、验证和测试数据
    train_split_idx = int(len(df) * train_ratio)
    val_split_idx = int(len(df) * (train_ratio + val_ratio))

    train_df = df.iloc[:train_split_idx].copy()
    val_df = df.iloc[train_split_idx:val_split_idx].copy()
    test_df = df.iloc[val_split_idx:].copy()

    # 定义输入和输出列
    input_cols = [col for col in df.columns if col.startswith('feature_')]
    output_cols = ['target']

    # 创建数据集
    train_dataset = TimeSeriesDataset(
        df_list=[train_df],
        input_col=input_cols,
        output_col=output_cols,
        input_len=seq_length,
        shift=-1  # 预测下一个时间步
    )

    val_dataset = TimeSeriesDataset(
        df_list=[val_df],
        input_col=input_cols,
        output_col=output_cols,
        input_len=seq_length,
        shift=-1
    )

    test_dataset = TimeSeriesDataset(
        df_list=[test_df],
        input_col=input_cols,
        output_col=output_cols,
        input_len=seq_length,
        shift=-1
    )

    return train_dataset, val_dataset, test_dataset, input_cols, output_cols


def compare_models():
    """
    比较原始LSTM和注意力增强LSTM的性能
    """
    print("=== 模型性能比较 ===")
    
    # 创建合成数据
    print("创建合成数据...")
    df = create_synthetic_data(n_samples=2000, seq_length=50, n_features=5)
    
    # 准备数据
    seq_length = 20
    train_dataset, val_dataset, test_dataset, input_cols, output_cols = prepare_data(df, seq_length)

    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    print(f"测试集大小: {len(test_dataset)}")
    print(f"输入特征: {input_cols}")
    print(f"输出特征: {output_cols}")

    # 创建数据加载器
    batch_size = 32
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # 获取输入输出维度
    sample_batch = next(iter(train_loader))
    input_size = sample_batch[0].shape[-1]
    output_size = sample_batch[1].shape[-1]
    
    print(f"输入维度: {input_size}")
    print(f"输出维度: {output_size}")
    
    # 模型配置
    model_config = {
        'input_size': input_size,
        'hidden_size': 64,
        'num_layers': 2,
        'output_size': output_size,
        'dropout': 0.1,
        'learning_rate': 1e-3,
    }
    
    models_to_test = [
        ('原始LSTM', LitLSTM(**model_config)),
        ('自注意力LSTM', LitLSTMWithAttention(
            **model_config,
            attention_type='self',
            use_attention_residual=True
        )),
        ('多头注意力LSTM', LitLSTMWithAttention(
            **model_config,
            attention_type='multi_head',
            num_attention_heads=8,
            use_attention_residual=True
        )),
    ]
    
    results = {}
    
    for model_name, model in models_to_test:
        print(f"\n训练 {model_name}...")
        
        # 设置训练器
        trainer = L.Trainer(
            max_epochs=10,
            accelerator='auto',
            devices=1,
            logger=False,
            enable_checkpointing=False,
            enable_progress_bar=True,
        )
        
        # 训练模型
        trainer.fit(model, train_loader, val_loader)
        
        # 测试模型
        test_results = trainer.test(model, test_loader, verbose=False)
        
        # 记录结果
        results[model_name] = {
            'test_loss': test_results[0]['test_loss'],
            'test_mae': test_results[0]['test_mae'],
            'test_rmse': test_results[0]['test_rmse'],
            'params': sum(p.numel() for p in model.parameters())
        }
        
        print(f"{model_name} - 测试损失: {results[model_name]['test_loss']:.4f}, "
              f"MAE: {results[model_name]['test_mae']:.4f}, "
              f"RMSE: {results[model_name]['test_rmse']:.4f}, "
              f"参数数量: {results[model_name]['params']:,}")
    
    # 打印比较结果
    print("\n=== 模型比较结果 ===")
    print(f"{'模型':<15} {'测试损失':<10} {'MAE':<10} {'RMSE':<10} {'参数数量':<12}")
    print("-" * 65)
    
    for model_name, metrics in results.items():
        print(f"{model_name:<15} {metrics['test_loss']:<10.4f} "
              f"{metrics['test_mae']:<10.4f} {metrics['test_rmse']:<10.4f} "
              f"{metrics['params']:<12,}")


def demonstrate_attention_visualization():
    """
    演示注意力权重的可视化（简化版本）
    """
    print("\n=== 注意力机制演示 ===")
    
    # 创建一个简单的模型用于演示
    model = LitLSTMWithAttention(
        input_size=5,
        hidden_size=32,
        num_layers=1,
        output_size=1,
        attention_type='self',
    )
    
    # 创建示例输入
    batch_size, seq_len, input_size = 1, 10, 5
    x = torch.randn(batch_size, seq_len, input_size)
    
    print(f"输入形状: {x.shape}")
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        output = model(x)
        print(f"输出形状: {output.shape}")
        print(f"预测值: {output.item():.4f}")
    
    print("注意力机制成功应用到LSTM输出序列上")


def main():
    """主函数"""
    print("注意力增强LSTM模型使用示例")
    print("=" * 50)
    
    # 设置随机种子
    L.seed_everything(42)
    
    try:
        # 比较不同模型的性能
        compare_models()
        
        # 演示注意力机制
        demonstrate_attention_visualization()
        
        print("\n🎉 示例运行完成！")
        print("\n使用建议:")
        print("1. 对于复杂的时间序列模式，注意力机制可以提高模型性能")
        print("2. 自注意力适合捕获序列内部的依赖关系")
        print("3. 多头注意力可以学习不同类型的注意力模式")
        print("4. 注意力机制会增加模型参数，需要权衡性能和复杂度")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        raise


if __name__ == "__main__":
    main()
