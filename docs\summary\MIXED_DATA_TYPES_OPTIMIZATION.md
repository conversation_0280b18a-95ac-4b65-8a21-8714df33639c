# 混合数据类型重采样优化总结

## 概述

对 `src/utils/influxql_helper.py` 文件中的 `_apply_resample_and_fill` 方法进行了重大优化，使其能够智能处理包含混合数据类型的 DataFrame，包括数值、布尔、字符串、时间等不同类型的列。

## 问题背景

原始的 `_apply_resample_and_fill` 方法使用 `df.resample(time_interval).mean()` 进行重采样，这种方法存在以下问题：

1. **数据丢失**：只能处理数值类型的列，会自动丢弃字符串、布尔等非数值列
2. **类型不一致**：重采样后的数据类型可能与原始数据类型不匹配
3. **填充策略单一**：所有列使用相同的填充方法，不适合混合数据类型

## 优化方案

### 1. 智能列分类

根据数据类型自动将 DataFrame 的列分为以下类别：

- **数值列**：`int64`, `float64`, `int32`, `float32`, `int16`, `float16`
- **布尔列**：`bool` 类型，包括因 NaN 导致转换为 `object` 的布尔列
- **字符串列**：`object`, `string` 类型
- **时间列**：`datetime64` 类型
- **其他列**：不属于以上类型的列

### 2. 分类重采样策略

针对不同数据类型采用不同的聚合方法：

```python
# 数值列 - 使用均值聚合
numeric_resampled = df[numeric_columns].resample(time_interval).mean()

# 布尔列 - 使用 first() 保持原始值特性
boolean_resampled = df[boolean_columns].resample(time_interval).first()

# 字符串列 - 使用 first() 保留第一个值
string_resampled = df[string_columns].resample(time_interval).first()

# 时间列 - 使用 first() 保留第一个值
datetime_resampled = df[datetime_columns].resample(time_interval).first()
```

### 3. 智能填充策略

根据数据类型和填充方法采用不同的填充策略：

#### 数值列填充
- `linear`：线性插值
- `nearest`：最近邻插值
- `ffill`：前向填充
- `bfill`：后向填充

#### 非数值列填充
- `linear`/`nearest`：自动降级为前向填充（因为不支持插值）
- `ffill`：前向填充
- `bfill`：后向填充

### 4. 列顺序保持

确保重采样后的 DataFrame 保持原有的列顺序，避免列顺序混乱。

### 5. 数据类型保持

尽可能保持原始数据类型，对于因重采样导致的类型变化（如整数变为浮点数）进行合理处理。

## 核心方法

### `_apply_resample_and_fill` 方法

<augment_code_snippet path="src/utils/influxql_helper.py" mode="EXCERPT">
```python
def _apply_resample_and_fill(
    self,
    df: pd.DataFrame,
    time_interval: str,
    fill_method: str,
) -> pd.DataFrame:
    """应用重采样和填充，智能处理混合数据类型"""
    if df.empty:
        return df
    
    # 保存原始列顺序
    original_columns = df.columns.tolist()
    
    # 分类不同数据类型的列
    numeric_columns = df.select_dtypes(include=['int64', 'float64', 'int32', 'float32', 'int16', 'float16']).columns.tolist()
    boolean_columns = df.select_dtypes(include=['bool']).columns.tolist()
    string_columns = df.select_dtypes(include=['object', 'string']).columns.tolist()
    datetime_columns = df.select_dtypes(include=['datetime64']).columns.tolist()
```
</augment_code_snippet>

### `_apply_fill_strategy` 方法

<augment_code_snippet path="src/utils/influxql_helper.py" mode="EXCERPT">
```python
def _apply_fill_strategy(
    self,
    df: pd.DataFrame,
    fill_method: str,
    numeric_columns: list,
    boolean_columns: list,
    string_columns: list,
    datetime_columns: list,
    other_columns: list,
) -> pd.DataFrame:
    """根据数据类型应用不同的填充策略"""
```
</augment_code_snippet>

## 功能特性

### ✅ 保留非数值列
- 字符串列：保留状态信息、操作员名称等
- 布尔列：保留报警状态、设备开关状态等
- 时间列：保留时间戳信息
- 其他类型：保留特殊数据类型

### ✅ 智能聚合策略
- **数值列**：使用 `mean()` 计算平均值
- **非数值列**：使用 `first()` 保留代表性值
- **自动降级**：不支持的操作自动降级为兼容方法

### ✅ 优化填充策略
- **数值列**：支持线性插值、最近邻插值等高级填充
- **非数值列**：使用前向/后向填充，避免插值错误
- **兼容处理**：不支持的填充方法自动降级

### ✅ 保持列顺序
- 重采样后严格按照原始列顺序重新组合
- 避免列顺序混乱影响后续处理

### ✅ 向后兼容
- 对于纯数值数据，处理结果与之前完全一致
- 现有代码无需修改即可享受新功能

## 使用示例

### 基本用法

```python
# 创建包含混合数据类型的 DataFrame
data = {
    'temperature': [25.1, 25.2, 25.3],  # 数值列
    'alarm_active': [True, False, True],  # 布尔列
    'status': ['NORMAL', 'WARNING', 'NORMAL'],  # 字符串列
    'timestamp': pd.to_datetime(['2025-01-01 10:00:00', '2025-01-01 10:00:01', '2025-01-01 10:00:02'])  # 时间列
}
df = pd.DataFrame(data, index=pd.date_range('2025-01-01 10:00:00', periods=3, freq='1s'))

# 应用重采样和填充
result = influx_query._apply_resample_and_fill(df, '30s', 'linear')
```

### 在分块查询中的应用

```python
# 在 batch_query_aligned_df 中自动应用
df = influx_query.batch_query_aligned_df(
    measurements=["sensor1", "sensor2"],
    field=["temperature", "status", "alarm"],  # 混合字段类型
    start_time=start_time,
    end_time=end_time,
    time_interval="1min",
    fill_method="linear",
    chunk_size="1h"
)
```

## 性能优势

### 1. 数据完整性
- **零数据丢失**：所有类型的列都被保留和处理
- **类型一致性**：尽可能保持原始数据类型
- **语义保持**：非数值数据的语义得到正确处理

### 2. 处理效率
- **分类处理**：针对不同类型采用最优策略
- **内存优化**：避免不必要的数据转换
- **错误减少**：减少因数据类型不匹配导致的错误

### 3. 灵活性
- **自适应**：自动识别和处理各种数据类型
- **可配置**：支持不同的填充策略
- **扩展性**：易于添加新的数据类型支持

## 测试验证

通过综合测试验证了以下场景：

1. **混合数据类型重采样**：数值、布尔、字符串、时间列的混合处理
2. **不同填充方法**：linear、ffill、bfill、nearest 等方法的正确应用
3. **列顺序保持**：重采样后列顺序与原始顺序一致
4. **数据类型保持**：重采样后数据类型的正确性
5. **NaN 值处理**：各种类型列中 NaN 值的正确填充

## 注意事项

1. **数据类型变化**：某些情况下数据类型可能发生合理变化（如整数聚合后变为浮点数）
2. **pandas 警告**：可能出现 pandas 版本相关的 FutureWarning，但不影响功能
3. **性能考虑**：处理大量混合类型数据时，性能可能略低于纯数值处理
4. **兼容性**：建议使用较新版本的 pandas 以获得最佳兼容性

## 总结

这次优化显著提升了 InfluxDB 查询工具处理混合数据类型的能力，使其能够：

- **完整保留**所有类型的数据列
- **智能处理**不同数据类型的重采样和填充
- **保持兼容**现有代码和使用方式
- **提供灵活**的配置选项

用户现在可以安全地查询和处理包含多种数据类型的时序数据，而无需担心数据丢失或类型不匹配问题。
