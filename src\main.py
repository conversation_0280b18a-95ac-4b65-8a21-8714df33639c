import os
import time
import logging
import lightning as L

from config import Configs, TrainConfig, Logger
from datetime import datetime
from data_loader import FlftrLoader
from model.lstm import LitLSTM
from lightning.pytorch.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
)
from lightning.pytorch.loggers import TensorBoardLogger

logger = logging.getLogger(__name__)


def train_lstm():
    logger.info("开始训练LSTM模型")

    config: TrainConfig = Configs.get_train_config()
    lstm_config = Configs.get_lstm_config()

    work_dir = f"data/train/{datetime.now().strftime('%Y%m%d')}"
    os.makedirs(work_dir, exist_ok=True)

    start_time = datetime.strptime("2024-01-01", "%Y-%m-%d")
    end_time = datetime.strptime("2024-12-31", "%Y-%m-%d")

    data_module = FlftrLoader(
        start_time=start_time, end_time=end_time, work_dir=work_dir, config=config
    )

    data_module.prepare_data()
    data_module.setup()

    train_loader = data_module.train_dataloader()
    batch = next(iter(train_loader))
    x, y = batch
    input_size = x.shape[-1]
    output_size = y.shape[-1]

    logger.info(
        f"输入特征维度: {input_size}, 输出特征维度: {output_size}, 数据批次形状 - 输入: {x.shape}, 输出: {y.shape}"
    )

    model = LitLSTM(
        input_size=input_size,
        hidden_size=lstm_config.hidden_size,
        num_layers=lstm_config.num_layers,
        output_size=output_size,
        dropout=lstm_config.dropout,
        bidirectional=lstm_config.bidirectional,
        learning_rate=lstm_config.learning_rate,
        weight_decay=lstm_config.weight_decay,
    )

    # 设置回调
    checkpoint_callback = ModelCheckpoint(
        monitor="val_loss",
        filename="lstm-{epoch:02d}-{val_loss:.4f}",
        save_top_k=3,
        mode="min",
    )

    early_stopping = EarlyStopping(
        monitor="val_loss",
        patience=config.early_stopping_patience,
        mode="min",
    )

    lr_monitor = LearningRateMonitor(logging_interval="epoch")

    # 设置日志记录器
    tensorboard_logger = TensorBoardLogger("logs", name="lstm")

    # 创建训练器
    trainer = L.Trainer(
        max_epochs=config.epochs,
        callbacks=[checkpoint_callback, early_stopping, lr_monitor],
        logger=tensorboard_logger,
        accelerator=config.accelerator,
        devices=config.devices,
        gradient_clip_val=config.gradient_clip_val,
    )

    # 训练模型
    trainer.fit(
        model=model,
        train_dataloaders=data_module.train_dataloader(),
        val_dataloaders=data_module.val_dataloader(),
    )

    # 评估模型
    trainer.test(model=model, dataloaders=data_module.test_dataloader())

    # 保存模型
    save_path = os.path.join(work_dir, "lstm_model.ckpt")
    trainer.save_checkpoint(save_path)
    print(f"模型已保存到 {save_path}")


if __name__ == "__main__":

    Configs.initialize()  # 初始化配置管理器
    Logger.initialize()  # 初始化日志记录器

    train_lstm()
