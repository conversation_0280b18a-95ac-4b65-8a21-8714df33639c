# 数字滤波器库

这是一个完整的数字滤波器库，专为退火炉带钢温度预测项目设计，提供了五种主要类型的滤波器。

## 📋 功能特性

- ✅ **完整的滤波器类型**：低通、高通、带通、带阻、卡尔曼滤波器
- ✅ **pandas Series 兼容**：所有函数接受并返回 pandas Series
- ✅ **多种实现方式**：支持不同的滤波器设计方法
- ✅ **参数验证**：完整的输入参数验证和错误处理
- ✅ **详细文档**：每个函数都有详细的中文文档
- ✅ **使用示例**：提供完整的使用示例和演示

## 🚀 快速开始

### 基本导入

```python
from src.filters import (
    low_pass_filter,
    high_pass_filter,
    band_pass_filter,
    band_stop_filter,
    kalman_filter
)
```

### 创建测试数据

```python
import numpy as np
import pandas as pd

# 创建包含多个频率成分的测试信号
t = np.linspace(0, 1, 1000)
signal_data = (
    np.sin(2 * np.pi * 5 * t) +      # 5Hz 成分
    0.5 * np.sin(2 * np.pi * 25 * t) +  # 25Hz 成分
    0.3 * np.sin(2 * np.pi * 50 * t) +  # 50Hz 成分
    np.random.normal(0, 0.1, len(t))     # 噪声
)
data = pd.Series(signal_data, index=t)
```

## 📚 滤波器详细说明

### 1. 低通滤波器 (Low-pass Filter)

去除高频噪声，保留低频信号成分。

```python
# 标准低通滤波器
filtered = low_pass_filter(
    data=data,
    cutoff_freq=15.0,        # 截止频率 (Hz)
    sampling_freq=1000.0,    # 采样频率 (Hz)
    order=4,                 # 滤波器阶数
    filter_type='butterworth' # 滤波器类型
)

# 移动平均滤波器（简单实现）
ma_filtered = moving_average_filter(data, window_size=20)

# 指数平滑滤波器
exp_filtered = exponential_smoothing_filter(data, alpha=0.3)
```

**支持的滤波器类型**：
- `butterworth`：Butterworth滤波器（默认）
- `chebyshev1`：Chebyshev I型滤波器
- `chebyshev2`：Chebyshev II型滤波器
- `elliptic`：椭圆滤波器

### 2. 高通滤波器 (High-pass Filter)

去除低频成分，保留高频信号。

```python
# 标准高通滤波器
filtered = high_pass_filter(
    data=data,
    cutoff_freq=20.0,
    sampling_freq=1000.0,
    order=4
)

# 差分滤波器（简单高通实现）
diff_filtered = difference_filter(data, order=1)

# 去趋势滤波器
detrend_filtered = detrend_filter(data, method='linear')
```

### 3. 带通滤波器 (Band-pass Filter)

只保留特定频率范围内的信号成分。

```python
# 标准带通滤波器
filtered = band_pass_filter(
    data=data,
    low_cutoff=20.0,     # 低截止频率
    high_cutoff=30.0,    # 高截止频率
    sampling_freq=1000.0,
    order=4
)

# 基于FFT的带通滤波器
fft_filtered = fft_band_pass_filter(
    data=data,
    low_cutoff=20.0,
    high_cutoff=30.0,
    sampling_freq=1000.0
)
```

### 4. 带阻滤波器 (Band-stop Filter)

去除特定频率范围内的信号成分。

```python
# 标准带阻滤波器
filtered = band_stop_filter(
    data=data,
    low_cutoff=45.0,
    high_cutoff=55.0,
    sampling_freq=1000.0
)

# 陷波滤波器（去除特定频率）
notch_filtered = notch_filter(
    data=data,
    notch_freq=50.0,        # 陷波频率
    sampling_freq=1000.0,
    quality_factor=30.0     # 品质因子
)
```

### 5. 卡尔曼滤波器 (Kalman Filter)

适用于含噪声的动态系统状态估计。

```python
# 标准卡尔曼滤波器
filtered = kalman_filter(
    data=data,
    process_variance=0.01,      # 过程噪声方差
    measurement_variance=0.1,   # 测量噪声方差
    initial_state=None,         # 初始状态（可选）
    initial_covariance=None     # 初始协方差（可选）
)

# 自适应卡尔曼滤波器
adaptive_filtered = adaptive_kalman_filter(
    data=data,
    initial_process_variance=0.01,
    initial_measurement_variance=0.1,
    adaptation_rate=0.01
)
```

## 🧪 测试和验证

运行测试脚本验证所有滤波器功能：

```bash
cd src/filters
python test_filters.py
```

运行使用示例：

```bash
python examples.py
```

## 📊 参数选择指南

### 截止频率选择
- **低通滤波器**：选择略高于感兴趣信号的最高频率
- **高通滤波器**：选择略低于感兴趣信号的最低频率
- **带通滤波器**：根据目标信号的频率范围设置

### 滤波器阶数
- **低阶数 (2-4)**：过渡带较宽，相位失真小
- **高阶数 (6-8)**：过渡带较窄，频率选择性好

### 卡尔曼滤波器参数
- **过程噪声方差**：反映系统动态的不确定性
  - 较小值：信号变化平滑
  - 较大值：允许快速跟踪变化
- **测量噪声方差**：反映测量的不确定性
  - 根据实际测量噪声水平设置

## ⚠️ 注意事项

1. **奈奎斯特频率**：截止频率必须小于采样频率的一半
2. **边界效应**：滤波器在信号开始和结束处可能产生边界效应
3. **相位延迟**：IIR滤波器可能引入相位延迟，使用 `filtfilt` 可以消除
4. **数据类型**：确保输入数据为 pandas Series 格式

## 🔧 故障排除

### 常见错误

1. **"截止频率必须小于奈奎斯特频率"**
   - 解决：降低截止频率或提高采样频率

2. **"输入数据必须是pandas Series对象"**
   - 解决：使用 `pd.Series()` 转换数据

3. **滤波效果不理想**
   - 检查参数设置是否合理
   - 尝试不同的滤波器类型或阶数

## 📈 性能优化

- 对于大数据集，考虑使用 FFT 方法
- 批量处理时可以重用滤波器参数
- 卡尔曼滤波器适合实时处理场景

## 🤝 贡献

如需添加新的滤波器类型或改进现有功能，请遵循现有的代码结构和文档风格。
