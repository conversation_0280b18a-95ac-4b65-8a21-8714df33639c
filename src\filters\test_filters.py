"""
滤波器测试脚本

该脚本用于测试所有滤波器的基本功能，确保它们能够正常工作。
"""

import numpy as np
import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入滤波器函数
try:
    from filters import (
        low_pass_filter,
        high_pass_filter,
        band_pass_filter,
        band_stop_filter,
        kalman_filter,
        moving_average_filter,
        notch_filter,
        exponential_smoothing_filter,
        difference_filter,
        detrend_filter
    )
    print("✅ 所有滤波器模块导入成功")
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)


def create_test_signal():
    """创建测试信号"""
    t = np.linspace(0, 1, 1000)
    # 创建包含多个频率成分的信号
    signal = (
        np.sin(2 * np.pi * 5 * t) +      # 5Hz
        0.5 * np.sin(2 * np.pi * 25 * t) +  # 25Hz
        0.3 * np.sin(2 * np.pi * 50 * t) +  # 50Hz
        np.random.normal(0, 0.1, len(t))     # 噪声
    )
    return pd.Series(signal, index=t, name='test_signal')


def test_low_pass_filters():
    """测试低通滤波器"""
    print("\n🔍 测试低通滤波器...")
    signal = create_test_signal()
    
    try:
        # 测试标准低通滤波器
        filtered = low_pass_filter(signal, cutoff_freq=15, sampling_freq=1000)
        assert len(filtered) == len(signal), "滤波后长度不匹配"
        assert isinstance(filtered, pd.Series), "返回类型不是pandas Series"
        print("  ✅ low_pass_filter 测试通过")
        
        # 测试移动平均滤波器
        ma_filtered = moving_average_filter(signal, window_size=10)
        assert len(ma_filtered) == len(signal), "移动平均滤波后长度不匹配"
        print("  ✅ moving_average_filter 测试通过")
        
        # 测试指数平滑滤波器
        exp_filtered = exponential_smoothing_filter(signal, alpha=0.3)
        assert len(exp_filtered) == len(signal), "指数平滑滤波后长度不匹配"
        print("  ✅ exponential_smoothing_filter 测试通过")
        
    except Exception as e:
        print(f"  ❌ 低通滤波器测试失败: {e}")
        return False
    
    return True


def test_high_pass_filters():
    """测试高通滤波器"""
    print("\n🔍 测试高通滤波器...")
    signal = create_test_signal()
    
    try:
        # 测试标准高通滤波器
        filtered = high_pass_filter(signal, cutoff_freq=20, sampling_freq=1000)
        assert len(filtered) == len(signal), "滤波后长度不匹配"
        print("  ✅ high_pass_filter 测试通过")
        
        # 测试差分滤波器
        diff_filtered = difference_filter(signal, order=1)
        assert len(diff_filtered) == len(signal), "差分滤波后长度不匹配"
        print("  ✅ difference_filter 测试通过")
        
        # 测试去趋势滤波器
        detrend_filtered = detrend_filter(signal, method='linear')
        assert len(detrend_filtered) == len(signal), "去趋势滤波后长度不匹配"
        print("  ✅ detrend_filter 测试通过")
        
    except Exception as e:
        print(f"  ❌ 高通滤波器测试失败: {e}")
        return False
    
    return True


def test_band_filters():
    """测试带通和带阻滤波器"""
    print("\n🔍 测试带通和带阻滤波器...")
    signal = create_test_signal()
    
    try:
        # 测试带通滤波器
        bp_filtered = band_pass_filter(
            signal, low_cutoff=20, high_cutoff=30, sampling_freq=1000
        )
        assert len(bp_filtered) == len(signal), "带通滤波后长度不匹配"
        print("  ✅ band_pass_filter 测试通过")
        
        # 测试带阻滤波器
        bs_filtered = band_stop_filter(
            signal, low_cutoff=20, high_cutoff=30, sampling_freq=1000
        )
        assert len(bs_filtered) == len(signal), "带阻滤波后长度不匹配"
        print("  ✅ band_stop_filter 测试通过")
        
        # 测试陷波滤波器
        notch_filtered = notch_filter(signal, notch_freq=50, sampling_freq=1000)
        assert len(notch_filtered) == len(signal), "陷波滤波后长度不匹配"
        print("  ✅ notch_filter 测试通过")
        
    except Exception as e:
        print(f"  ❌ 带通/带阻滤波器测试失败: {e}")
        return False
    
    return True


def test_kalman_filter():
    """测试卡尔曼滤波器"""
    print("\n🔍 测试卡尔曼滤波器...")
    
    # 创建带噪声的平滑信号
    t = np.linspace(0, 2*np.pi, 100)
    true_signal = np.sin(t)
    noisy_signal = true_signal + np.random.normal(0, 0.2, len(t))
    signal = pd.Series(noisy_signal, index=t, name='noisy_signal')
    
    try:
        # 测试标准卡尔曼滤波器
        kf_filtered = kalman_filter(
            signal, 
            process_variance=0.01, 
            measurement_variance=0.04
        )
        assert len(kf_filtered) == len(signal), "卡尔曼滤波后长度不匹配"
        assert isinstance(kf_filtered, pd.Series), "返回类型不是pandas Series"
        print("  ✅ kalman_filter 测试通过")
        
        # 计算滤波效果
        mse_original = np.mean((noisy_signal - true_signal)**2)
        mse_filtered = np.mean((kf_filtered.values - true_signal)**2)
        improvement = (mse_original - mse_filtered) / mse_original * 100
        print(f"  📊 卡尔曼滤波改善程度: {improvement:.1f}%")
        
    except Exception as e:
        print(f"  ❌ 卡尔曼滤波器测试失败: {e}")
        return False
    
    return True


def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试错误处理...")
    
    try:
        # 测试空数据
        empty_series = pd.Series([])
        try:
            low_pass_filter(empty_series, cutoff_freq=10, sampling_freq=1000)
            print("  ❌ 空数据错误处理失败")
            return False
        except ValueError:
            print("  ✅ 空数据错误处理正确")
        
        # 测试无效参数
        signal = create_test_signal()
        try:
            low_pass_filter(signal, cutoff_freq=-10, sampling_freq=1000)
            print("  ❌ 负频率错误处理失败")
            return False
        except ValueError:
            print("  ✅ 负频率错误处理正确")
        
        # 测试截止频率超过奈奎斯特频率
        try:
            low_pass_filter(signal, cutoff_freq=600, sampling_freq=1000)
            print("  ❌ 超奈奎斯特频率错误处理失败")
            return False
        except ValueError:
            print("  ✅ 超奈奎斯特频率错误处理正确")
            
    except Exception as e:
        print(f"  ❌ 错误处理测试失败: {e}")
        return False
    
    return True


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("🧪 数字滤波器库测试")
    print("=" * 60)
    
    tests = [
        test_low_pass_filters,
        test_high_pass_filters,
        test_band_filters,
        test_kalman_filter,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！滤波器库工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
