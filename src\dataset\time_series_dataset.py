import pandas as pd
import torch
import logging
import numpy as np
from torch.utils.data import Dataset, DataLoader
from typing import Optional, Union, List


class TimeSeriesDataset(Dataset):
    """时序数据Dataset"""

    def __init__(
        self,
        df_list: list[pd.DataFrame],
        input_col: list[str],
        output_col: list[str],
        input_len: int = 10,
        output_len: int = 1,
    ):
        """
        时序数据集，包含多个时间序列子集
        迭代器取样时会返回连续的时间序列样本，长度为seq_length

        :param df_list: List of DataFrames (每个DataFrame是一个子集)
        :param input_col: 输入目标列名
        :param output_col: 预测目标列名
        :param input_len: 每个样本的时间序列长度
        :param output_len: 每个项目的输出序列长度
        """
        self.input_len = input_len
        self.output_len = output_len
        self.input_col = input_col
        self.output_col = output_col
        self.logger = logging.getLogger("TimeSeriesDataset")

        # 验证列名是否存在
        if len(df_list) > 0:
            for col in input_col:
                if col not in df_list[0].columns:
                    raise ValueError(f"输入列 {col} 不存在于数据集中")
            for col in output_col:
                if col not in df_list[0].columns:
                    raise ValueError(f"输出列 {col} 不存在于数据集中")

        # 验证df_list长度
        if len(df_list) == 0:
            raise ValueError("df_list不能为空")

        # 复制一份数据
        self.data = []
        for df in df_list:
            if len(df) >= self.input_len + self.output_len:
                self.data.append(df)
            else:
                self.logger.warning(f"排除数据长度{len(df)}小于序列长度{self.input_len} + 输出长度{self.output_len}")

        # 如果所有数据帧都被过滤掉了，抛出错误
        if len(self.data) == 0:
            raise ValueError(f"所有数据帧长度均小于序列长度{self.input_len}，无法创建数据集")

        # 缓存每个df的起始和结束索引范围
        self.data_lengths = [len(df) for df in self.data]
        self.df_boundaries = []
        start_idx = 0
        for length in self.data_lengths:
            end_idx = start_idx + length - self.input_len - self.output_len
            if end_idx >= start_idx:  # 确保边界有效
                self.df_boundaries.append((start_idx, end_idx))
                start_idx = end_idx + 1

        # 计算总长度
        self.length = sum(df_end - df_start + 1 for df_start, df_end in self.df_boundaries)

        # 预计算索引映射以加速__getitem__
        self._precompute_indices()

    def _precompute_indices(self):
        """预计算索引映射以加速样本获取"""
        self.idx_mapping = []
        for df_idx, (start_idx, end_idx) in enumerate(self.df_boundaries):
            for local_idx in range(end_idx - start_idx + 1):
                self.idx_mapping.append((df_idx, local_idx))

    def __len__(self):
        return self.length

    def __getitem__(self, idx: int) -> tuple[torch.Tensor, torch.Tensor]:
        """
        获取一个连续的时间序列样本
        :param idx: 样本的索引
        :return: 输入数据、目标数据
        """
        # 使用预计算的索引映射直接获取df_idx和local_idx
        if idx < 0 or idx >= self.length:
            raise IndexError(f"索引{idx}超出范围[0, {self.length-1}]")

        df_idx, local_idx = self.idx_mapping[idx]

        # 获取连续的时间序列数据
        sequence = self.data[df_idx].iloc[local_idx : local_idx + self.input_len + self.output_len]

        features = sequence.iloc[:self.input_len][self.input_col].values
        target = sequence.iloc[self.input_len:][self.output_col].values

        # 转换为Tensor
        features_tensor = torch.tensor(features, dtype=torch.float32)
        target_tensor = torch.tensor(target, dtype=torch.float32)

        return features_tensor, target_tensor


class TimeSeriesDatasetAutoregressive(Dataset):
    """自回归预测时序数据Dataset"""

    def __init__(
        self,
        df_list: list[pd.DataFrame],
        lookback: int = 10,
        horizon: int = 1,
        input_col: Optional[list[str]] = None,
    ):
        """
        时序数据集，包含多个时间序列子集
        迭代器取样时会返回连续的时间序列样本，长度为seq_length

        :param df_list: List of DataFrames (每个DataFrame是一个子集)
        :param lookback: 输入窗口大小（历史时间步数）
        :param horizon: 预测的时间步数（未来目标）
        :param input_col: 输入列名，默认为None（使用所有列）
        """
        self.lookback = lookback
        self.horizon = horizon
        self.seq_length = self.lookback + self.horizon
        self.logger = logging.getLogger("TimeSeriesDatasetAutoregressive")
        self.input_col = input_col

        # 验证df_list长度
        if len(df_list) == 0:
            raise ValueError("df_list不能为空")

        # 验证列名是否存在
        if input_col is not None and len(df_list) > 0:
            for col in input_col:
                if col not in df_list[0].columns:
                    raise ValueError(f"输入列 {col} 不存在于数据集中")

        # 直接过滤掉长度小于seq_length的数据帧
        self.data = []
        for df in df_list:
            if len(df) >= self.seq_length:
                self.data.append(df)
            else:
                self.logger.warning(
                    f"警告：排除了长度为{len(df)}的数据帧，因为它小于所需的序列长度{self.seq_length}"
                )

        # 如果所有数据帧都被过滤掉了，抛出错误
        if len(self.data) == 0:
            raise ValueError(
                f"所有数据帧的长度都小于所需的序列长度{self.seq_length}，无法创建数据集"
            )

        # 缓存每个df的起始和结束索引范围
        self.data_lengths = [len(df) for df in self.data]
        self.df_boundaries = []
        start_idx = 0
        for length in self.data_lengths:
            end_idx = start_idx + length - self.seq_length
            if end_idx >= start_idx:  # 确保边界有效
                self.df_boundaries.append((start_idx, end_idx))
                start_idx = end_idx + 1

        # 计算总长度
        self.length = sum(
            df_end - df_start + 1 for df_start, df_end in self.df_boundaries
        )

        # 预计算索引映射以加速__getitem__
        self._precompute_indices()

    def _precompute_indices(self):
        """预计算索引映射以加速样本获取"""
        self.idx_mapping = []
        for df_idx, (start_idx, end_idx) in enumerate(self.df_boundaries):
            for local_idx in range(end_idx - start_idx + 1):
                self.idx_mapping.append((df_idx, local_idx))

    def __len__(self):
        return self.length

    def __getitem__(self, idx: int) -> tuple[torch.Tensor, torch.Tensor]:
        """
        获取一个连续的时间序列样本
        :param idx: 样本的索引
        :return: 输入数据、目标数据
        """
        # 使用预计算的索引映射直接获取df_idx和local_idx
        if idx < 0 or idx >= self.length:
            raise IndexError(f"索引{idx}超出范围[0, {self.length-1}]")

        df_idx, local_idx = self.idx_mapping[idx]

        # 获取连续的时间序列数据
        sequence = self.data[df_idx].iloc[local_idx : local_idx + self.seq_length]

        # 划分输入和目标
        features = sequence.iloc[: self.lookback].values
        target = sequence.iloc[self.lookback :].values

        # 转换为Tensor
        features_tensor = torch.tensor(features, dtype=torch.float32)
        target_tensor = torch.tensor(target, dtype=torch.float32)

        return features_tensor, target_tensor


if __name__ == "__main__":
    df1 = pd.DataFrame({"A": range(100), "B": range(100, 200), "C": range(1000, 1100)})

    df2 = pd.DataFrame(
        {"A": range(101, 151), "B": range(201, 251), "C": range(1101, 1151)}
    )

    # dataset = TimeSeriesDatasetAutoregressive([df1, df2], 9, 1)
    dataset = TimeSeriesDataset([df1, df2], ["A", "B", "C"], ["C"], input_len=10, output_len=5)
    dataloader = DataLoader(dataset, batch_size=2, shuffle=True, num_workers=4)

    print(len(dataset))

    for j in range(len(dataset)):
        feature, target = dataset[j]
        print(f"索引{j}")
        print(f"feature: {feature}")
        print(f"target: {target}")
