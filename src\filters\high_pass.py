"""
高通滤波器模块

该模块实现了数字高通滤波器，用于去除信号中的低频成分，保留高频成分。
支持多种滤波器类型：Butterworth、Chebyshev I、Chebyshev II、Elliptic等。
"""

import numpy as np
import pandas as pd
from scipy import signal
from typing import Optional


def high_pass_filter(
    data: pd.Series,
    cutoff_freq: float,
    sampling_freq: float,
    order: int = 4,
    filter_type: str = "butterworth",
    rp: Optional[float] = None,
    rs: Optional[float] = None,
) -> pd.Series:
    """
    应用高通滤波器到输入信号

    参数:
        data (pd.Series): 输入信号数据
        cutoff_freq (float): 截止频率 (Hz)
        sampling_freq (float): 采样频率 (Hz)
        order (int): 滤波器阶数，默认为4
        filter_type (str): 滤波器类型，可选：
            - 'butterworth': Butterworth滤波器（默认）
            - 'chebyshev1': Chebyshev I型滤波器
            - 'chebyshev2': Chebyshev II型滤波器
            - 'elliptic': 椭圆滤波器
        rp (float, optional): 通带最大衰减 (dB)，用于Chebyshev I和椭圆滤波器
        rs (float, optional): 阻带最小衰减 (dB)，用于Chebyshev II和椭圆滤波器

    返回:
        pd.Series: 滤波后的信号数据

    异常:
        ValueError: 当参数不合法时抛出

    示例:
        >>> import pandas as pd
        >>> import numpy as np
        >>>
        >>> # 创建测试信号
        >>> t = np.linspace(0, 1, 1000)
        >>> signal_data = np.sin(2 * np.pi * 5 * t) + 0.5 * np.sin(2 * np.pi * 50 * t)
        >>> data = pd.Series(signal_data)
        >>>
        >>> # 应用高通滤波器
        >>> filtered_data = high_pass_filter(data, cutoff_freq=20, sampling_freq=1000)
    """

    # 参数验证
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    if cutoff_freq <= 0:
        raise ValueError("截止频率必须大于0")

    if sampling_freq <= 0:
        raise ValueError("采样频率必须大于0")

    if cutoff_freq >= sampling_freq / 2:
        raise ValueError("截止频率必须小于奈奎斯特频率 (采样频率的一半)")

    if order <= 0:
        raise ValueError("滤波器阶数必须大于0")

    # 计算归一化截止频率
    nyquist_freq = sampling_freq / 2
    normalized_cutoff = cutoff_freq / nyquist_freq

    try:
        # 根据滤波器类型设计滤波器
        if filter_type.lower() == "butterworth":
            b, a = signal.butter(order, normalized_cutoff, btype="high", analog=False)  # type: ignore

        elif filter_type.lower() == "chebyshev1":
            if rp is None:
                rp = 1.0  # 默认通带衰减1dB
            b, a = signal.cheby1(order, rp, normalized_cutoff, btype="high", analog=False)  # type: ignore

        elif filter_type.lower() == "chebyshev2":
            if rs is None:
                rs = 40.0  # 默认阻带衰减40dB
            b, a = signal.cheby2(order, rs, normalized_cutoff, btype="high", analog=False)  # type: ignore

        elif filter_type.lower() == "elliptic":
            if rp is None:
                rp = 1.0  # 默认通带衰减1dB
            if rs is None:
                rs = 40.0  # 默认阻带衰减40dB
            b, a = signal.ellip(order, rp, rs, normalized_cutoff, btype="high", analog=False)  # type: ignore
        else:
            raise ValueError(f"不支持的滤波器类型: {filter_type}")

        # 应用滤波器
        filtered_data = signal.filtfilt(b, a, data.values)

        # 创建结果Series，保持原始索引
        result = pd.Series(filtered_data, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"滤波器设计或应用失败: {str(e)}")


def difference_filter(data: pd.Series, order: int = 1) -> pd.Series:
    """
    差分滤波器（高通滤波的一种简单实现）

    参数:
        data (pd.Series): 输入信号数据
        order (int): 差分阶数，默认为1

    返回:
        pd.Series: 滤波后的信号数据
    """
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if order <= 0:
        raise ValueError("差分阶数必须大于0")

    if len(data) <= order:
        raise ValueError("数据长度必须大于差分阶数")

    # 计算差分
    filtered_data = data.diff(periods=order)

    # 处理NaN值（使用0填充）
    filtered_data = filtered_data.fillna(0)

    return filtered_data


def detrend_filter(data: pd.Series, method: str = "linear") -> pd.Series:
    """
    去趋势滤波器（去除低频趋势成分）

    参数:
        data (pd.Series): 输入信号数据
        method (str): 去趋势方法，可选：
            - 'linear': 线性去趋势（默认）
            - 'constant': 去除均值

    返回:
        pd.Series: 滤波后的信号数据
    """
    if not isinstance(data, pd.Series):
        raise ValueError("输入数据必须是pandas Series对象")

    if len(data) == 0:
        raise ValueError("输入数据不能为空")

    try:
        if method.lower() == "linear":
            # 线性去趋势
            detrended_data = signal.detrend(data.values, type="linear")  # type: ignore
        elif method.lower() == "constant":
            # 去除均值
            detrended_data = signal.detrend(data.values, type="constant")  # type: ignore
        else:
            raise ValueError(f"不支持的去趋势方法: {method}")

        # 创建结果Series，保持原始索引
        result = pd.Series(detrended_data, index=data.index, name=data.name)

        return result

    except Exception as e:
        raise ValueError(f"去趋势处理失败: {str(e)}")
