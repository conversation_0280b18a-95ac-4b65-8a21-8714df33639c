#!/usr/bin/env python3
"""
多字段查询功能使用示例
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.influxql_helper import InfluxQueryWrapper, InfluxQuery
import pandas as pd


def example_1_basic_multi_field():
    """示例1: 基础多字段查询"""
    print("=== 示例1: 基础多字段查询 ===")
    
    # 查询多个字段
    qw = InfluxQueryWrapper(
        "sensor_data", 
        field=["temperature", "pressure", "humidity"]
    )
    
    # 添加时间范围条件
    qw.ge("time", "2025-01-01T00:00:00Z")
    qw.le("time", "2025-01-01T23:59:59Z")
    
    # 添加其他条件
    qw.eq("location", "workshop_1")
    
    # 排序和限制
    qw.order_by_desc("time")
    qw.limit(1000)
    
    query = qw.build()
    print(f"生成的SQL: {query}")
    print()


def example_2_multi_measurement_multi_field():
    """示例2: 多测量点多字段查询"""
    print("=== 示例2: 多测量点多字段查询 ===")
    
    # 查询多个测量点的多个字段
    qw = InfluxQueryWrapper(
        "furnace_zone_1", "furnace_zone_2", "furnace_zone_3",
        field=["temperature", "pressure"]
    )
    
    # 添加条件
    qw.gt("time", "2025-01-01T08:00:00Z")
    qw.lt("time", "2025-01-01T18:00:00Z")
    qw.gt("temperature", 500)  # 温度大于500度
    
    query = qw.build()
    print(f"生成的SQL: {query}")
    print()


def example_3_aggregation_multi_field():
    """示例3: 多字段聚合查询"""
    print("=== 示例3: 多字段聚合查询 ===")
    
    # 对多个字段进行聚合
    qw = InfluxQueryWrapper(
        "production_line",
        field=["temperature", "pressure", "speed", "quality_score"],
        aggregation="mean"
    )
    
    # 按时间分组
    qw.group_by_time("1h", fill="linear")
    
    # 添加时间范围
    qw.ge("time", "2025-01-01T00:00:00Z")
    qw.le("time", "2025-01-07T23:59:59Z")
    
    query = qw.build()
    print(f"生成的SQL: {query}")
    print()


def example_4_real_world_usage():
    """示例4: 实际使用场景"""
    print("=== 示例4: 实际使用场景 - 退火炉数据查询 ===")
    
    # 模拟真实的退火炉数据查询
    measurements = [
        "Luzi.PLC03.DB34,REAL170",  # 温度传感器1
        "Luzi.PLC03.DB34,REAL174",  # 温度传感器2
    ]
    
    # 假设我们要查询多个字段（如果存在的话）
    fields = ["value"]  # 在实际使用中，可能有多个字段如 ["value", "quality", "status"]
    
    # 创建查询
    qw = InfluxQueryWrapper(*measurements, field=fields)
    
    # 添加时间范围
    qw.gt("time", "2025-04-16T00:29:00Z")
    qw.le("time", "2025-04-16T00:30:00Z")
    
    # 排序
    qw.order_by("time")
    
    query = qw.build()
    print(f"生成的SQL: {query}")
    
    # 如果有真实的数据库连接，可以执行查询
    try_real_query = False  # 设置为True来尝试真实查询
    
    if try_real_query:
        try:
            # InfluxDB连接参数
            host = "***********"
            port = 8086
            username = "root"
            password = "123456"
            database = "annealing_furnace"
            
            influx_query = InfluxQuery(host, port, username, password, database)
            
            # 使用batch_query_aligned_df进行多字段查询
            df = influx_query.batch_query_aligned_df(
                measurements=measurements,
                field=fields,  # 多字段查询
                start_time="2025-04-16T00:29:00Z",
                end_time="2025-04-16T00:30:00Z",
                time_interval="1s",
                fill_method="linear",
                resample=True
            )
            
            if df is not None and not df.empty:
                print(f"查询结果形状: {df.shape}")
                print(f"列名: {df.columns.tolist()}")
                print("前5行数据:")
                print(df.head())
            else:
                print("查询结果为空")
                
            influx_query.close()
            
        except Exception as e:
            print(f"数据库查询失败: {e}")
    
    print()


def example_5_advanced_conditions():
    """示例5: 高级条件查询"""
    print("=== 示例5: 高级条件查询 ===")
    
    # 复杂的多字段查询
    qw = InfluxQueryWrapper(
        "process_monitor",
        field=["temperature", "pressure", "flow_rate", "efficiency"]
    )
    
    # 多种条件组合
    qw.ge("time", "2025-01-01T00:00:00Z")
    qw.le("time", "2025-01-31T23:59:59Z")
    qw.gt("temperature", 200)
    qw.lt("temperature", 800)
    qw.ge("pressure", 1.0)
    qw.le("pressure", 10.0)
    qw.in_("status", ["running", "normal", "optimal"])
    qw.neq("error_code", 0)
    
    # 排序和限制
    qw.order_by_desc("time")
    qw.limit(5000)
    
    query = qw.build()
    print(f"生成的SQL: {query}")
    print()


def example_6_different_aggregations():
    """示例6: 不同聚合函数的多字段查询"""
    print("=== 示例6: 不同聚合函数的多字段查询 ===")
    
    aggregations = ["mean", "max", "min", "sum", "count"]
    
    for agg in aggregations:
        qw = InfluxQueryWrapper(
            "sensor_readings",
            field=["temperature", "pressure", "humidity"],
            aggregation=agg
        )
        
        qw.group_by_time("1h")
        qw.ge("time", "2025-01-01T00:00:00Z")
        qw.le("time", "2025-01-01T23:59:59Z")
        
        query = qw.build()
        print(f"{agg.upper()}聚合查询: {query}")
    
    print()


if __name__ == "__main__":
    print("多字段查询功能使用示例\n")
    
    example_1_basic_multi_field()
    example_2_multi_measurement_multi_field()
    example_3_aggregation_multi_field()
    example_4_real_world_usage()
    example_5_advanced_conditions()
    example_6_different_aggregations()
    
    print("所有示例运行完成！")
    print("\n使用说明:")
    print("1. field参数现在支持字符串（单字段）或列表/元组（多字段）")
    print("2. 多字段查询时，列名格式为'测量点名称_字段名称'（多字段）或'测量点名称'（单字段）")
    print("3. 所有聚合函数都支持多字段操作")
    print("4. batch_query_aligned_df方法已更新以支持多字段查询")
