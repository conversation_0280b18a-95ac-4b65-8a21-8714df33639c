# ProcessManager - Python多进程管理器

这是一个简单实用的Python多进程管理器，用于创建、管理和监控多个进程。它提供了一种便捷的方式来启动、监控和终止多个进程，并收集它们的执行结果。

## 功能特点

- 简单易用的API，易于集成到现有项目
- 支持最大进程数限制
- 优雅终止进程机制（通过退出标志）
- 进程状态监控和结果收集
- 异常处理和错误日志
- 资源自动清理

## 安装

无需额外安装，只需将`process_manager.py`文件复制到你的项目中即可。

## 使用示例

```python
from src.utils.process_manager import ProcessManager

# 创建进程管理器，最多允许5个进程同时运行
pm = ProcessManager(max_processes=5)

# 添加进程
pm.add_process("process1", target_function, args=(arg1, arg2), kwargs={"key": value})

# 检查进程状态
active_processes = pm.get_active_processes()

# 获取进程信息
info = pm.get_process_info("process1")

# 检查进程结果
completed = pm.check_results()

# 获取结果
result = pm.get_result("process1")

# 终止进程
pm.terminate_process("process1")

# 终止所有进程
pm.terminate_all()
```

更详细的使用示例请参阅`example_usage.py`文件。

## API参考

### 初始化

```python
ProcessManager(max_processes=None, log_level=logging.INFO)
```

- `max_processes`: 最大进程数，默认为CPU核心数
- `log_level`: 日志级别

### 主要方法

| 方法 | 说明 |
| --- | --- |
| `add_process(process_id, target_func, args=(), kwargs=None, daemon=True)` | 添加新进程 |
| `check_results(block=False, timeout=None)` | 检查进程结果 |
| `get_result(process_id)` | 获取指定进程结果 |
| `terminate_process(process_id, wait=True, timeout=3.0)` | 终止指定进程 |
| `terminate_all(wait=True, timeout=3.0)` | 终止所有进程 |
| `is_process_alive(process_id)` | 检查进程是否存活 |
| `get_active_processes()` | 获取活动进程列表 |
| `get_process_info(process_id)` | 获取进程信息 |
| `wait_for_processes(process_ids=None, timeout=None)` | 等待指定进程完成 |

## 退出机制

`ProcessManager`支持两种退出机制：

1. **优雅终止**: 通过退出标志(`exit_flag`)通知进程自行终止
2. **强制终止**: 如果进程在规定时间内未响应，将使用`terminate()`强制终止

## 线程安全性

目前的实现主要设计用于单线程环境中控制多个进程。如果需要在多线程环境中使用，需要添加额外的锁机制。

## 注意事项

- 所有被管理的进程应该定期检查`exit_flag`，以支持优雅终止
- 进程ID必须是唯一的字符串
- 进程函数的返回值会被存储并可通过`get_result()`获取

## 限制

- 不支持进程间通信(IPC)机制，仅提供基本的结果返回
- 不支持分布式进程管理 