import json
import logging
from pathlib import Path
from typing import Optional
import pandas as pd
import numpy as np
import lightning as L
import os

from datetime import datetime
from zoneinfo import ZoneInfo
from torch.utils.data import DataLoader
from config import Configs
from dataset.time_series_dataset import TimeSeriesDataset
from utils.influxql_helper import InfluxQuery
from track import StripTracker, TrackingMsg, Strip
from enums import FileNameEnum as fne

from utils.data_utils import (
    fill_na_with_moving_average,
    downsample,
    split_series_data,
    split_data,
    normalize_dfs,
    normalize_dfs_by_params,
)


class AFLoader(L.LightningDataModule):
    def __init__(self, start_time: datetime, end_time: datetime, work_dir: str):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.start_time = start_time
        self.end_time = end_time
        self.work_dir = work_dir
        self.meta_dir = os.path.join(self.work_dir, "meta")
        self.data_dir = os.path.join(self.work_dir, "data")

        # 检查文件目录
        os.makedirs(self.work_dir, exist_ok=True)
        os.makedirs(self.meta_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)

        # config
        self.config = Configs.get_train_config()
        self.influxdb_config = Configs.get_influx_db_config()

        # 数据
        self.data: Optional[pd.DataFrame] = None
        self.normalize_meta: dict = {}
        self.train_list: list[pd.DataFrame] = []
        self.test_list: list[pd.DataFrame] = []
        self.valid_list: list[pd.DataFrame] = []

    def prepare_data(self):
        """
        从InfluxDB读取数据并保存至本地

        Returns:
            pd.DataFrame: 包含所有测量点数据的DataFrame，索引为时间戳
        """
        try:
            self.logger.info("开始从InfluxDB读取退火炉数据...")

            # 创建InfluxDB连接
            influx_query = InfluxQuery(
                host=self.influxdb_config.host,
                port=self.influxdb_config.port,
                username=self.influxdb_config.username,
                password=self.influxdb_config.password,
                database=self.influxdb_config.database,
            )

            # 获取所有测量点名称
            measurements = list(self.config.measurements.keys())
            self.logger.info(f"准备查询 {len(measurements)} 个测量点")

            # 转换时间格式为ISO8601
            # start_time_iso = self.start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            # end_time_iso = self.end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

            self.logger.info(
                f"查询时间范围: {self.start_time.strftime("%Y-%m-%dT%H:%M:%SZ")} 到 {self.end_time.strftime("%Y-%m-%dT%H:%M:%SZ")}"
            )

            # 使用batch_query_aligned_df进行批量查询
            # 支持多字段查询，这里使用默认的"value"字段
            df = influx_query.batch_query_aligned_df(
                measurements=measurements,
                field="value",  # 可以改为多字段，如 ["value", "quality", "status"]
                start_time=self.start_time,
                end_time=self.end_time,
                chunk_size="7d",
            )

            # 关闭数据库连接
            influx_query.close()

            if df is None or df.empty:
                self.logger.warning("查询结果为空，请检查时间范围和测量点名称")
                return pd.DataFrame()

            self.logger.info(f"成功查询到数据，形状: {df.shape}")

            df = df.rename(columns=self.config.measurements)  # type: ignore
            self.logger.info(f"重命名了 {len(self.config.measurements)} 个列名")

            # 确保返回的是DataFrame类型
            if not isinstance(df, pd.DataFrame):
                self.logger.error(f"查询结果类型错误: {type(df)}")
                return pd.DataFrame()

            # 保存原始数据到本地
            self._save_data_to_local(
                df,
                f"{fne.SOURCE.value}.csv",
                keep_idx=True,
                idx_label=self.config.index_column,
            )
            # self.data = df
            self.logger.info("数据准备完成")

        except Exception as e:
            self.logger.error(f"从InfluxDB读取数据失败: {e}")
            raise e

    def _save_data_to_local(
        self,
        df: pd.DataFrame,
        file_name: str,
        keep_idx: bool = False,
        idx_label: str = "INDEX",
    ) -> None:
        """
        将数据保存到本地文件

        Args:
            df: 要保存的DataFrame
        """
        try:

            if not self.config.save_data:
                return

            if keep_idx:
                df.to_csv(
                    Path(self.data_dir, file_name), index=True, index_label=idx_label
                )
            else:
                df.to_csv(Path(self.data_dir, file_name), index=False)
        except Exception as e:
            self.logger.error(f"保存数据到本地失败: {e}")
            # 不抛出异常，因为这不是关键功能

    def setup(self, stage: Optional[str] = None) -> None:
        try:
            # 检查数据
            if self.data is None:
                self.logger.warning("数据为空，尝试重新读取")

                if os.path.exists(Path(self.data_dir, f"{fne.SOURCE.value}.csv")):
                    self.logger.info("从本地读取数据")
                    self.data = pd.read_csv(
                        Path(self.data_dir, f"{fne.SOURCE.value}.csv")
                    )
                else:
                    self.logger.info("从InfluxDB读取数据")
                    self.prepare_data()
                    self.data = pd.read_csv(
                        Path(self.data_dir, f"{fne.SOURCE.value}.csv")
                    )

            # 钢卷跟踪处理
            self._strip_tracking()

            # 数据预处理
            self._preprocess_data()

            # 分割数据
            train_list, valid_list, test_list = self._split_data()

            # 归一化数据
            self._normalize_data(train_list, valid_list, test_list)

        except Exception as e:
            raise e

    def _strip_tracking(self):
        """
        钢卷跟踪

        原始数据记录了多个开卷机上料情况和焊缝位置，
        需要将钢卷和焊缝位置匹配
        """
        if self.data is None:
            raise ValueError("数据为空")

        def safe_to_numeric(value, default=-1):
            """安全地将值转换为数值，如果转换失败或为NaN则返回默认值"""
            numeric_value = pd.to_numeric(value, errors="coerce")
            return numeric_value if pd.notna(numeric_value) else default

        utc8_tz = ZoneInfo("Asia/Shanghai")
        strip_tricker = StripTracker(self.data_dir)
        tracking = {col: [] for col in self.config.track_columns}
        for row in self.data.itertuples(index=False, name="DataRow"):
            timestamp = datetime.fromisoformat(
                getattr(row, self.config.index_column)
            ).astimezone(utc8_tz)
            selected = bool(row.SELECTED)
            strip1 = Strip(
                name=str(row.STRIP_NAME_1).strip(),
                type=str(row.STRIP_TYPE_1).strip(),
                width=safe_to_numeric(row.STRIP_WIDTH_1),
                thick=safe_to_numeric(row.STRIP_THICK_1),
                length=safe_to_numeric(row.STRIP_LENGTH_1),
                weight=safe_to_numeric(row.STRIP_WEIGHT_1),
            )
            strip2 = Strip(
                name=str(row.STRIP_NAME_2).strip(),
                type=str(row.STRIP_TYPE_2).strip(),
                width=safe_to_numeric(row.STRIP_WIDTH_2),
                thick=safe_to_numeric(row.STRIP_THICK_2),
                length=safe_to_numeric(row.STRIP_LENGTH_2),
                weight=safe_to_numeric(row.STRIP_WEIGHT_2),
            )
            track_msg = TrackingMsg(
                weld1=safe_to_numeric(row.WELD_POSITION_1, default=0),
                weld2=safe_to_numeric(row.WELD_POSITION_2, default=0),
                speed=safe_to_numeric(row.SPEED, default=0),
            )

            strip_tricker.update(timestamp, selected, strip1, strip2, track_msg)
            heating_strip: Optional[Strip] = strip_tricker.get_heating_strip()

            if heating_strip is not None:
                tracking["STRIP_NAME"].append(heating_strip.name)
                tracking["STRIP_TYPE"].append(heating_strip.type)
                tracking["STRIP_WIDTH"].append(heating_strip.width)
                tracking["STRIP_THICK"].append(heating_strip.thick)
                tracking["STRIP_LENGTH"].append(heating_strip.length)
                tracking["STRIP_WEIGHT"].append(heating_strip.weight)
                tracking["WELD"].append(heating_strip.weld2)
            else:
                # 确保结果长度对齐
                for col in self.config.track_columns:
                    tracking[col].append(None)

        for col in self.config.track_columns:
            self.data[col] = tracking[col]
        self.logger.info("钢卷跟踪完成...")

        strip_tricker.save_tracking_data()
        self._save_data_to_local(self.data, f"{fne.TRACKED.value}.csv")

    def _preprocess_data(self):
        if self.data is None or len(self.data) == 0:
            raise ValueError("数据为空")

        # 1. 行列排序
        columns = [self.config.index_column, *self.config.input_columns]
        self.data = self.data[columns]
        self.data = self.data.sort_values(by=self.config.index_column, ascending=True)
        self.logger.info("排序完成...")

        self._save_data_to_local(self.data, f"{fne.SORTED.value}.csv")

        # 2. 时间戳转换为索引
        self.data.loc[:, self.config.index_column] = pd.to_datetime(
            self.data[self.config.index_column], format="mixed"
        ).dt.tz_convert("Asia/Shanghai")
        self.data = self.data.set_index(self.config.index_column)
        self.logger.info("重建索引完成...")

        self._save_data_to_local(self.data, f"{fne.INDEXED.value}.csv", keep_idx=True)

        # 3.异常数据处理
        self.data = self.data.dropna()  # 先删除NAN行，再进行异常值处理。

        if "SPEED" in self.data.columns:
            self.data = self.data[self.data["SPEED"] > 0]
        if "STRIP_TEMP_NOF" in self.data.columns:
            self.data = self.data[self.data["STRIP_TEMP_NOF"] > 450]
        if "STRIP_TEMP_RTF" in self.data.columns:
            self.data = self.data[self.data["STRIP_TEMP_RTF"] > 450]
        if "STRIP_TEMP_SF" in self.data.columns:
            self.data = self.data[self.data["STRIP_TEMP_SF"] > 376]

        # 检查时序数据连续性，删除长度小于3600的序列
        self.data = self._check_time_series_continuity(self.data, min_length=3600)

        self._save_data_to_local(
            self.data, f"{fne.ANOMALY_REMOVED.value}.csv", keep_idx=True
        )

        # 4. 缺失值处理
        self.data = fill_na_with_moving_average(self.data)

        self._save_data_to_local(self.data, f"{fne.FILLED.value}.csv", keep_idx=True)

        # 5. 重采样/时间戳对齐
        self.data = downsample(self.data, freq=self.config.freq)

        self._save_data_to_local(self.data, f"{fne.RESAMPLED.value}.csv", keep_idx=True)

        # 6. 删除依旧为NAN的行
        self.data = (
            self.data.dropna()
        )  # 删除依旧为NAN的行，即重采样后没有对应数据的行。

        self._save_data_to_local(self.data, f"{fne.FINAL.value}.csv", keep_idx=True)

        self.logger.info("数据预处理完成...")

    def _check_time_series_continuity(
        self, df: pd.DataFrame, min_length: int = 3600
    ) -> pd.DataFrame:
        """
        检查时序数据的连续性，识别并删除长度小于min_length的时序序列段

        Args:
            df: 带有时间索引的DataFrame
            min_length: 最小序列长度，小于此值的序列段将被删除

        Returns:
            pd.DataFrame: 清理后的连续时序数据
        """
        if df is None or df.empty:
            self.logger.warning("输入数据为空")
            return df

        if not isinstance(df.index, pd.DatetimeIndex):
            self.logger.error("DataFrame的索引不是时间戳类型")
            return df

        self.logger.info(f"开始检查时序数据连续性，原始数据长度: {len(df)}")

        # 1. 计算时间间隔统计信息
        time_diffs = df.index.to_series().diff().dropna()

        if len(time_diffs) == 0:
            self.logger.warning("无法计算时间间隔")
            return df

        # 计算时间间隔的统计信息
        median_interval = time_diffs.median()
        mean_interval = time_diffs.mean()
        std_interval = time_diffs.std()

        self.logger.info(
            f"时间间隔统计 - 中位数: {median_interval}, 平均值: {mean_interval}, 标准差: {std_interval}"
        )

        # 2. 确定间隔阈值（使用中位数的3倍作为阈值）
        gap_threshold = pd.Timedelta(median_interval * 2)
        self.logger.info(f"使用间隔阈值: {gap_threshold}")

        # 3. 识别数据中断点
        break_points = time_diffs > gap_threshold
        break_indices = break_points[break_points].index

        self.logger.info(f"发现 {len(break_indices)} 个数据中断点")

        # 4. 分割连续序列段
        segments = []
        start_idx = 0

        for break_idx in break_indices:
            # 找到中断点在原DataFrame中的位置
            try:
                break_pos_raw = df.index.get_loc(break_idx)
                if isinstance(break_pos_raw, slice):
                    break_pos = break_pos_raw.start or 0
                elif isinstance(break_pos_raw, np.ndarray):
                    break_pos = int(break_pos_raw[0]) if len(break_pos_raw) > 0 else 0
                elif isinstance(break_pos_raw, (list, tuple)):
                    break_pos = int(break_pos_raw[0]) if len(break_pos_raw) > 0 else 0
                else:
                    break_pos = int(break_pos_raw)
            except Exception as e:
                self.logger.warning(f"获取中断点位置时出错: {e}")
                break_pos = 0

            # 提取当前段
            segment = df.iloc[start_idx:break_pos]
            if len(segment) > 0:
                segments.append(segment)
                self.logger.info(
                    f"序列段 {len(segments)}: 长度 {len(segment)}, 时间范围 {segment.index[0]} 到 {segment.index[-1]}"
                )

            start_idx = break_pos

        # 添加最后一段
        if start_idx < len(df):
            last_segment = df.iloc[start_idx:]
            if len(last_segment) > 0:
                segments.append(last_segment)
                self.logger.info(
                    f"序列段 {len(segments)}: 长度 {len(last_segment)}, 时间范围 {last_segment.index[0]} 到 {last_segment.index[-1]}"
                )

        # 5. 过滤短序列段
        valid_segments = []
        total_removed_points = 0

        for i, segment in enumerate(segments, 1):
            if len(segment) >= min_length:
                valid_segments.append(segment)
                self.logger.info(f"保留序列段 {i}: 长度 {len(segment)} >= {min_length}")
            else:
                total_removed_points += len(segment)
                self.logger.info(f"删除序列段 {i}: 长度 {len(segment)} < {min_length}")

        # 6. 合并有效序列段
        if not valid_segments:
            self.logger.warning("没有满足最小长度要求的序列段")
            return pd.DataFrame()

        # 按时间顺序合并所有有效段
        result_df = pd.concat(valid_segments, axis=0).sort_index()

        self.logger.info(f"连续性检查完成:")
        self.logger.info(f"  - 原始数据点数: {len(df)}")
        self.logger.info(f"  - 删除数据点数: {total_removed_points}")
        self.logger.info(f"  - 保留数据点数: {len(result_df)}")
        self.logger.info(f"  - 有效序列段数: {len(valid_segments)}")

        return result_df

    def _split_data(
        self,
    ) -> tuple[list[pd.DataFrame], list[pd.DataFrame], list[pd.DataFrame]]:
        self.logger.info("开始分割数据")

        if self.data is None or len(self.data) == 0:
            raise ValueError("数据为空")

        if len(self.config.split_ratio) != 3:
            raise ValueError("split_ratio长度必须等于3")

        split_ratio_tuple = (
            self.config.split_ratio[0],
            self.config.split_ratio[1],
            self.config.split_ratio[2],
        )

        # 1.根据时间索引空缺分割时序数据
        data_segments = split_series_data(
            self.data, time_gap=self.config.freq, min_length=self.config.min_data_length
        )

        for idx, segment in enumerate(data_segments):
            self._save_data_to_local(
                segment, f"{fne.SEGMENT.value}_{idx}.csv", keep_idx=True
            )

        train_list, valid_list, test_list = split_data(
            data_segments, split_ratio=split_ratio_tuple
        )

        for idx, segment in enumerate(train_list):
            self._save_data_to_local(
                segment, f"{fne.TRAIN.value}_{idx}.csv", keep_idx=True
            )
        for idx, segment in enumerate(valid_list):
            self._save_data_to_local(
                segment, f"{fne.VALID.value}_{idx}.csv", keep_idx=True
            )
        for idx, segment in enumerate(test_list):
            self._save_data_to_local(
                segment, f"{fne.TEST.value}_{idx}.csv", keep_idx=True
            )

        self.logger.info("数据分割完成")

        return train_list, valid_list, test_list

    def _normalize_data(
        self,
        train_list: list[pd.DataFrame],
        valid_list: list[pd.DataFrame],
        test_list: list[pd.DataFrame],
    ) -> None:
        self.logger.info("开始归一化数据")

        self.train_list, self.normalize_meta = normalize_dfs(
            train_list, method=self.config.normalize_method
        )
        self.valid_list = normalize_dfs_by_params(valid_list, self.normalize_meta)
        self.test_list = normalize_dfs_by_params(test_list, self.normalize_meta)

        # 保存归一化元信息
        with open(
            os.path.join(self.meta_dir, f"{fne.NORMALIZE_META.value}.json"), "w"
        ) as f:
            json.dump(self.normalize_meta, f)

        self.logger.info("数据归一化完成")

    def train_dataloader(self) -> DataLoader:
        dataset = TimeSeriesDataset(
            self.train_list,
            self.config.input_columns,
            self.config.target_columns,
            input_len=self.config.input_len,
            output_len=self.config.output_len,
        )

        return DataLoader(dataset, batch_size=self.config.batch_size, shuffle=False)

    def val_dataloader(self) -> DataLoader:
        dataset = TimeSeriesDataset(
            self.valid_list,
            self.config.input_columns,
            self.config.target_columns,
            input_len=self.config.input_len,
            output_len=self.config.output_len,
        )

        return DataLoader(dataset, batch_size=self.config.batch_size, shuffle=False)

    def test_dataloader(self) -> DataLoader:
        dataset = TimeSeriesDataset(
            self.test_list,
            self.config.input_columns,
            self.config.target_columns,
            input_len=self.config.input_len,
            output_len=self.config.output_len,
        )

        return DataLoader(dataset, batch_size=self.config.batch_size, shuffle=False)
