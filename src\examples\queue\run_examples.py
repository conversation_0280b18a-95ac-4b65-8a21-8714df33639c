#!/usr/bin/env python3
"""
Queue通信示例运行脚本
"""

import sys
import subprocess
import time


def run_basic_example():
    """运行基础Queue通信示例"""
    print("=" * 60)
    print("运行基础Queue通信示例")
    print("=" * 60)

    try:
        result = subprocess.run(
            [sys.executable, "queue_communication_example.py"],
            capture_output=False,
            text=True,
            timeout=60,
        )
        if result.returncode == 0:
            print("\n✅ 基础示例运行成功")
        else:
            print(f"\n❌ 基础示例运行失败，返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("\n⏰ 基础示例运行超时")
    except Exception as e:
        print(f"\n❌ 运行基础示例时出错: {e}")


def run_advanced_example():
    """运行高级Queue通信示例"""
    print("\n" + "=" * 60)
    print("运行高级Queue通信示例（多生产者-多消费者）")
    print("=" * 60)

    try:
        result = subprocess.run(
            [sys.executable, "advanced_queue_example.py"],
            capture_output=False,
            text=True,
            timeout=120,
        )
        if result.returncode == 0:
            print("\n✅ 高级示例运行成功")
        else:
            print(f"\n❌ 高级示例运行失败，返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("\n⏰ 高级示例运行超时")
    except Exception as e:
        print(f"\n❌ 运行高级示例时出错: {e}")


def main():
    """主函数"""
    print("🚀 Queue进程间通信示例演示")
    print("本脚本将依次运行两个示例：")
    print("1. 基础Queue通信示例 - 一个发送进程，一个接收进程")
    print("2. 高级Queue通信示例 - 多个生产者和消费者进程")

    choice = input(
        "\n请选择要运行的示例 (1: 基础示例, 2: 高级示例, 3: 全部运行): "
    ).strip()

    if choice == "1":
        run_basic_example()
    elif choice == "2":
        run_advanced_example()
    elif choice == "3":
        run_basic_example()
        time.sleep(2)  # 等待2秒
        run_advanced_example()
    else:
        print("❌ 无效选择，请输入 1、2 或 3")
        return

    print("\n🎉 示例演示完成！")

    # 检查是否生成了结果文件
    import os

    if os.path.exists("task_results.json"):
        print("📄 高级示例的结果已保存到 task_results.json 文件中")


if __name__ == "__main__":
    main()
