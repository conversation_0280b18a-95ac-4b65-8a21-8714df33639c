import numpy as np
import pandas as pd
import logging

from typing import Optional

logger = logging.getLogger(__name__)


def fill_na_with_moving_average(
    df: pd.DataFrame,
    columns: Optional[list] = None,
    window_size: int = 5,
    max_iterations: int = 10,
    min_periods: int = 1,
    fill_zero: bool = False,
) -> pd.DataFrame:
    """
    移动平均填充NaN
    :param df: 需要插补的DataFrame
    :param columns: 需要插补的列
    :param window_size: 移动窗口大小
    :param max_iterations: 最大迭代次数
    :param min_periods: 窗口中最小有效值数量
    :param fill_zero: 迭代后仍是NaN的是否填充0
    :return: 插补后的DataFrame
    """
    if not isinstance(window_size, int) or window_size <= 0:
        raise ValueError("window_size 必须是大于0的整数")
    if not isinstance(max_iterations, int) or max_iterations <= 0:
        raise ValueError("max_iterations 必须是大于0的整数")

    filled_df = df.copy()

    # 如果没有指定列，则选择所有数值列
    if columns is None:
        columns = filled_df.select_dtypes(include="number").columns.tolist()

    for column in columns:
        if column in filled_df.columns:

            # 统计迭代次数
            iterations = 0
            # 持续迭代，直到指定列的NaN值被完全填充
            while filled_df[column].isna().sum() > 0:

                if iterations >= max_iterations:
                    logger.warning(
                        f"列'{column}'的NaN值在达到最大迭代次数({max_iterations})后仍未完全填充"
                    )
                    if fill_zero:
                        filled_df[column] = filled_df[column].fillna(0)
                    break

                # 计算移动平均
                filled_series = (
                    filled_df[column]
                    .rolling(window=window_size, min_periods=min_periods)
                    .mean()
                )
                # 用计算的移动平均值填充NaN
                filled_df[column] = filled_df[column].fillna(filled_series)
                iterations += 1
        else:
            logger.warning(f"列'{column}'不存在")

    return filled_df


def downsample_by_time_weighted(
    source: pd.DataFrame, interval: str, offset: Optional[str] = None
) -> pd.DataFrame:
    """
    时间加权下采样
    :param source: 需要下采样的DataFrame
    :param interval: 采样间隔 '1s' '1min', '1h', '1d'
    :param offset: 偏移量，重采样后的时间索引偏移量
    :return: 下采样结果
    """

    # 检查索引类型是否为时间戳
    if not isinstance(source.index, pd.DatetimeIndex):
        raise ValueError("DataFrame的索引不是时间戳类型，必须是DatetimeIndex类型")

    # 获取开始时间和结束时间，生成下采样结果的时间索引
    start_time = source.index.min().floor(interval)
    end_time = source.index.max().ceil(interval)
    time_index = pd.date_range(start=start_time, end=end_time, freq=interval)

    # 生成新索引的DataFrame
    resampled = source.reindex(time_index)

    # 遍历索引，从源DataFrame中获取
    for i in range(len(resampled) - 1):
        start = resampled.index[i]
        end = resampled.index[i + 1]
        group = source[(source.index >= start) & (source.index <= end)]

        if group.empty:
            # 如果没有数据，跳过或填充为NaN
            resampled.loc[start] = pd.NA
            continue

        # 获取前一条数据
        if group.index[0] == source.index[0]:
            last_record = group.iloc[0]
        else:
            last_record = source.loc[: group.index[0]].iloc[-2]

        # 上一条数据与本组数据第一条时间间隔检查，如果大于一个采样间隔，则用本组数据的第一行数据代替
        if abs(last_record.name - group.index[0]) > pd.Timedelta(interval):
            last_record = group.iloc[0]

        # 将上一条数据的时间戳更改为重采样开始时间，并插入到group的第一行
        last_record.name = start
        group = pd.concat([pd.DataFrame([last_record]), group], axis=0)

        # 计算时间间隔，最后一行的时间间隔用end计算
        group["time_diff"] = group.index.to_series().shift(-1) - group.index
        group.loc[group.index[-1], "time_diff"] = end - group.index[-1]
        group["time_diff"] = group["time_diff"].dt.total_seconds()

        # 加权计算，只有数值列才加权，其他类型的列
        for col in group.columns:
            if pd.api.types.is_numeric_dtype(group[col]) & (col not in ["time_diff"]):
                value = (group[col] * group["time_diff"]).sum() / group[
                    "time_diff"
                ].sum()
                resampled.loc[start, col] = value
            elif col not in ["time_diff"]:
                resampled.loc[start, col] = group.loc[group.index[-1], col]
            else:
                # time_diff列不做处理
                continue

    # 索引偏移
    if offset is not None:
        resampled.index = resampled.index + pd.Timedelta(offset)

    return resampled


def downsample(df: pd.DataFrame, freq: str, agg_func="mean") -> pd.DataFrame:
    """
    对DataFrame按采样间隔进行下采样
    :param df: 包含时序数据的DataFrame，其中时间是索引，值是要进行采样的列
    :param freq: 采样频率，'D'表示按天采样，'H'表示按小时采样，'5T'表示每5分钟采样
    :param agg_func: 聚合函数，默认为mean，可以是其他如sum、min、max等
    :return 下采样后的数据，包含每个采样区间的平均值。
    """
    # 确保时间索引是pandas的时间格式
    if not pd.api.types.is_datetime64_any_dtype(df.index):
        raise ValueError("DataFrame的索引需要是时间类型")

    down_sampled_df = df.resample(freq).agg(agg_func)

    return down_sampled_df


def smooth(
    df: pd.DataFrame,
    method: str = "SMA",
    columns: Optional[list[str]] = None,
    window: int = 5,
    weights: Optional[list[float]] = None,
    span: Optional[int] = None,
) -> pd.DataFrame:
    """
    对时序数据进行平滑处理
    Parameters:
    :param df: 包含时间戳和时序数据
    :param method: 平滑方法 ('SMA', 'WMA', 'EMA')，默认为 'SMA'
    :param columns: 要平滑的列名，默认为 'None'，表示对所有数值列进行平滑
    :param window: 窗口大小，用于 'SMA' 和 'WMA'
    :param weights: 权重列表，用于 'WMA'，若为 None，表示不使用
    :param span: 平滑因子，用于 'EMA'，若为 None，表示不使用
    :return 平滑后的DataFrame
    """
    # 检查索引类型是否为时间戳
    if not isinstance(df.index, pd.DatetimeIndex):
        raise ValueError("DataFrame的索引不是时间戳类型，必须是DatetimeIndex类型")

    if method == "WMA":
        if weights is None:
            raise ValueError("For WMA, 'weights' must be provided.")
        if len(weights) != window:
            raise ValueError(
                f"For WMA, 'weights' length must be equal to the window size ({window})."
            )
    elif method == "EMA":
        if span is None:
            raise ValueError("For EMA, 'span' must be provided.")
    elif method == "SMA":
        if window <= 0:
            raise ValueError("For SMA, 'window' must be greater than 0.")
    else:
        raise ValueError(
            f"Method '{method}' is not supported. Use 'SMA', 'WMA', or 'EMA'."
        )

    smooth_df = df.copy()

    # 如果没有指定列，则选择所有数值列
    if columns is None:
        columns = smooth_df.select_dtypes(include="number").columns.tolist()

    for column in columns:
        # 检查column是否存在
        if column not in smooth_df.columns:
            print(f"[Warning]--列'{column}'不存在")
            continue

        if method == "SMA":
            smooth_df[column] = smooth_df[column].rolling(window=window).mean()
        elif method == "WMA":
            smooth_df[column] = (
                smooth_df[column]
                .rolling(window=window)
                .apply(lambda x: sum(weights * x), raw=True)
            )
        elif method == "EMA":
            smooth_df[column] = smooth_df[column].ewm(span=span, adjust=False).mean()

    return smooth_df


def split_series_data(
    df: pd.DataFrame, time_gap: str = "1D", min_length=256
) -> list[pd.DataFrame]:
    """
    将 DataFrame 按时间索引的连续性分割成多个子序列。

    :param df: 带有时间索引的 DataFrame
    :param time_gap: 时间间隔阈值，默认为 '1D'（1 天）
    :param min_length: 序列最小长度，小于此值丢弃不要
    :return 包含多个连续子序列的列表。
    """
    # 检查索引类型是否为时间戳
    if not isinstance(df.index, pd.DatetimeIndex):
        raise ValueError("DataFrame的索引不是时间戳类型，必须是DatetimeIndex类型")

    # 计算时间索引的差值
    time_diff = df.index.to_series().diff()

    # 找到不连续的时间点
    break_points = time_diff > pd.Timedelta(time_gap)

    # 获取不连续时间点的索引
    break_indices = np.where(break_points)[0]

    # 根据不连续点分割数据
    segments = []
    start = 0
    for end in break_indices:
        segment = df.iloc[start:end]
        if len(segment) >= min_length:
            segments.append(df.iloc[start:end])
        start = end

    last_segment = df.iloc[start:]
    if len(last_segment) >= min_length:
        segments.append(last_segment)  # 添加最后一个子序列

    return segments


def split_data(
    df_list: list[pd.DataFrame],
    split_ratio: tuple[float, float, float] = (0.8, 0.1, 0.1),
    min_length: int = 256,
) -> tuple[list[pd.DataFrame], list[pd.DataFrame], list[pd.DataFrame]]:
    """
    将时序数据集分割成训练集、验证集和测试集
    :param df_list: 时序数据子序列集, 要求按时间顺序排列
    :param split_ratio: 划分数据集比例 (训练集, 验证集, 测试集)
    :param min_length: 子序列最小长度

    :return: 训练集、验证集和测试集
    """
    # 校验输入
    if not df_list:
        raise ValueError("输入列表不能为空")

    # 验证split_ratio
    total_ratio = sum(split_ratio)
    if total_ratio != 1.0:
        raise ValueError("split_ratio项的和必须等于1")

    for ratio in split_ratio:
        if ratio <= 0 or ratio >= 1:
            raise ValueError("split_ratio项的值必须在 (0, 1) 之间")

    if min_length < 1:
        raise ValueError("子序列最小长度 min_length 必须大于等于 1")

    # 过滤长度不足的子序列
    valid_df_list = [df for df in df_list if len(df) >= min_length]
    if not valid_df_list:
        raise ValueError("没有满足最小长度要求的子序列")

    # 计算每个子序列的长度并累计
    df_lengths = [len(df) for df in valid_df_list]
    total_length = sum(df_lengths)

    # 计算分割点
    train_end = int(total_length * split_ratio[0])
    val_end = train_end + int(total_length * split_ratio[1])

    # 初始化结果列表
    train_list = []
    val_list = []
    test_list = []

    # 分割数据
    current_pos = 0
    for df in valid_df_list:
        df_len = len(df)
        next_pos = current_pos + df_len

        # 完全在训练集中
        if next_pos <= train_end:
            train_list.append(df)
        # 跨越训练集和验证集
        elif current_pos < train_end <= next_pos <= val_end:
            split_point = train_end - current_pos
            train_part = df.iloc[:split_point]
            val_part = df.iloc[split_point:]

            # 检查子序列长度是否满足最小长度要求
            if len(train_part) >= min_length:
                train_list.append(train_part)
            if len(val_part) >= min_length:
                val_list.append(val_part)
        # 完全在验证集中
        elif train_end <= current_pos and next_pos <= val_end:
            val_list.append(df)
        # 跨越验证集和测试集
        elif current_pos < val_end <= next_pos:
            split_point = val_end - current_pos
            if current_pos < train_end:
                # 跨越三个集合
                train_split = train_end - current_pos
                train_part = df.iloc[:train_split]
                val_part = df.iloc[train_split:split_point]
                test_part = df.iloc[split_point:]

                # 检查子序列长度是否满足最小长度要求
                if len(train_part) >= min_length:
                    train_list.append(train_part)
                if len(val_part) >= min_length:
                    val_list.append(val_part)
                if len(test_part) >= min_length:
                    test_list.append(test_part)
            else:
                # 只跨越验证集和测试集
                val_part = df.iloc[:split_point]
                test_part = df.iloc[split_point:]

                # 检查子序列长度是否满足最小长度要求
                if len(val_part) >= min_length:
                    val_list.append(val_part)
                if len(test_part) >= min_length:
                    test_list.append(test_part)
        # 完全在测试集中
        else:
            test_list.append(df)

        current_pos = next_pos

    return train_list, val_list, test_list


def normalize(
    df: pd.DataFrame, columns: Optional[list[str]] = None, method="min-max"
) -> tuple[pd.DataFrame, dict]:
    """
    对DataFrame中的指定列进行归一化，并返回归一化参数以便后续返归一化。


    :param df: 输入的DataFrame。
    :param columns: 需要归一化的列名，可以是单个列名字符串或列名列表。
    :param method: 归一化方法，可选 'min-max' 或 'z-score'，默认为 'min-max'。

    :return
    pd.DataFrame: 归一化后的DataFrame。
    dict: 归一化参数，包含每列的最小值、最大值、均值、标准差等信息。
    """
    df = df.copy()  # 避免修改原始DataFrame
    normalization_params = {}

    # 如果没有指定列，则选择所有数值列
    if columns is None:
        columns = df.select_dtypes(include="number").columns.tolist()

    if isinstance(columns, str):
        columns = [columns]

    for col in columns:
        if method == "min-max":
            # 最小-最大归一化
            col_min = df[col].min()
            col_max = df[col].max()
            df[col] = (df[col] - col_min) / (col_max - col_min)
            normalization_params[col] = {
                "method": "min-max",
                "min": col_min,
                "max": col_max,
            }
        elif method == "z-score":
            # Z-score标准化
            col_mean = df[col].mean()
            col_std = df[col].std()
            df[col] = (df[col] - col_mean) / col_std
            normalization_params[col] = {
                "method": "z-score",
                "mean": col_mean,
                "std": col_std,
            }
        else:
            raise ValueError("不支持的归一化方法。请选择 'min-max' 或 'z-score'。")

    return df, normalization_params


def normalize_by_params(df: pd.DataFrame, normalization_params: dict) -> pd.DataFrame:
    """
    根据归一化参数对DataFrame中的列进行归一化
    :param df: 数据
    :param normalization_params: 归一化参数，包含每列的归一化方法及相关参数
    :return: 归一化后的数据
    """
    df = df.copy()

    for col, params in normalization_params.items():
        if params["method"] == "min-max":
            # 最小-最大归一化
            col_min = params["min"]
            col_max = params["max"]
            df[col] = (df[col] - col_min) / (col_max - col_min)
        elif params["method"] == "z-score":
            # Z-score标准化
            col_mean = params["mean"]
            col_std = params["std"]
            df[col] = (df[col] - col_mean) / col_std
        else:
            raise ValueError("不支持的归一化方法。请选择 'min-max' 或 'z-score'")

    return df


def normalize_dfs_by_params(
    df_list: list[pd.DataFrame], normalization_params: dict
) -> list[pd.DataFrame]:
    """
    根据归一化参数对DataFrame列表中的列进行归一化
    :param df_list:
    :param normalization_params:
    :return:
    """
    if len(df_list) == 0:
        raise ValueError("df_list不能为空")

    normalized_df_list = []
    for df in df_list:
        df_normalized = df.copy()
        for col, params in normalization_params.items():
            if params["method"] == "min-max":
                col_min = params["min"]
                col_max = params["max"]
                df_normalized[col] = (df[col] - col_min) / (col_max - col_min)
            elif params["method"] == "z-score":
                col_mean = params["mean"]
                col_std = params["std"]
                df_normalized[col] = (df[col] - col_mean) / col_std
        normalized_df_list.append(df_normalized)

    return normalized_df_list


def normalize_dfs(
    df_list: list[pd.DataFrame],
    columns: Optional[list[str]] = None,
    method: str = "min-max",
) -> tuple[list[pd.DataFrame], dict]:
    """
    使用所有DataFrame的全局参数对指定列进行归一化。

    :param df_list: 包含多个DataFrame的列表。
    :param columns: 需要归一化的列名列表。
    :param method: 归一化方法，可选 'min-max' 或 'z-score'，默认为 'min-max'。

    :return
    List[pd.DataFrame]: 归一化后的DataFrame列表。
    dict: 归一化参数，包含每列的全局最小值、最大值、均值、标准差等信息。
    """
    # 检查输入列表是否为空
    if len(df_list) == 0:
        raise ValueError("df_list不能为空")

    # 如果没有指定列，则选择所有数值列
    if columns is None:
        columns = df_list[0].select_dtypes(include="number").columns.tolist()

    # 初始化归一化参数字典
    normalization_params = {}

    # 计算全局参数
    if method == "min-max":
        for col in columns:
            min_values = [df[col].min() for df in df_list]
            max_values = [df[col].max() for df in df_list]
            col_min = min(min_values)
            col_max = max(max_values)
            normalization_params[col] = {
                "method": "min-max",
                "min": col_min,
                "max": col_max,
            }
    elif method == "z-score":
        for col in columns:
            # 计算全局均值和标准差
            all_values = pd.concat([df[col] for df in df_list])
            col_mean = all_values.mean()
            col_std = all_values.std()
            # 确保标准差不为零
            if col_std == 0:
                col_std = 1.0  # 防止除零错误
            normalization_params[col] = {
                "method": "z-score",
                "mean": col_mean,
                "std": col_std,
            }
    else:
        raise ValueError("不支持的归一化方法。请选择 'min-max' 或 'z-score'。")

    # 使用计算的参数对每个DataFrame进行归一化
    normalized_df_list = []
    for df in df_list:
        df_normalized = df.copy()
        for col in columns:
            if col in normalization_params:
                params = normalization_params[col]
                if params["method"] == "min-max":
                    col_min = params["min"]
                    col_max = params["max"]
                    # 处理最大值和最小值相等的情况
                    if col_max == col_min:
                        df_normalized[col] = 0.5  # 当所有值相等时设为0.5
                    else:
                        df_normalized[col] = (df[col] - col_min) / (col_max - col_min)
                elif params["method"] == "z-score":
                    col_mean = params["mean"]
                    col_std = params["std"]
                    df_normalized[col] = (df[col] - col_mean) / col_std
        normalized_df_list.append(df_normalized)

    return normalized_df_list, normalization_params


def denormalize(df: pd.DataFrame, normalization_params: dict) -> pd.DataFrame:
    """
    根据归一化参数对DataFrame中的列进行返归一化。

    :param df : 归一化后的DataFrame。
    :param normalization_params: 归一化参数，包含每列的归一化方法及相关参数。

    :return
    pd.DataFrame: 返归一化后的DataFrame。
    """
    df = df.copy()  # 避免修改原始DataFrame

    for col, params in normalization_params.items():
        if params["method"] == "min-max":
            # 最小-最大返归一化
            col_min = params["min"]
            col_max = params["max"]
            df[col] = df[col] * (col_max - col_min) + col_min
        elif params["method"] == "z-score":
            # Z-score返归一化
            col_mean = params["mean"]
            col_std = params["std"]
            df[col] = df[col] * col_std + col_mean

    return df


def denormalize_dataframes(
    df_list: list[pd.DataFrame], normalization_params: dict
) -> list[pd.DataFrame]:
    """
    根据归一化参数对DataFrame列表中的列进行返归一化。

    :param df_list: 包含多个DataFrame的列表。
    :param normalization_params: 归一化参数，包含每列的归一化方法及相关参数。

    :return
    List[pd.DataFrame]: 返归一化后的DataFrame列表。
    """
    denormalized_df_list = []

    for df in df_list:
        df_denormalized = denormalize(df, normalization_params)
        denormalized_df_list.append(df_denormalized)

    return denormalized_df_list
