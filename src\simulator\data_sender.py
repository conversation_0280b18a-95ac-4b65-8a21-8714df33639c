from config import InfluxDBCofing
from utils import influxql_helper


class DataSender:
    def __init__(self, influx_config: InfluxDBCofing):
        self.influx_config = influx_config
        self.influx_query = influxql_helper.InfluxQuery(
            self.influx_config.host,
            self.influx_config.port,
            self.influx_config.username,
            self.influx_config.password,
            self.influx_config.database,
        )

    def send_data(self, data):
        pass
