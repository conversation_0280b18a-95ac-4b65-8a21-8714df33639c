# 多字段查询功能使用指南

## 概述

InfluxQueryWrapper 现在支持多字段查询功能，允许您在单个查询中同时获取多个字段的数据。这个功能对于需要同时分析多个相关指标的场景非常有用。

## 主要特性

- ✅ 支持单字段和多字段查询
- ✅ 支持多字段聚合操作（mean, max, min, sum, count等）
- ✅ 支持多测量点多字段查询
- ✅ 自动处理列名冲突
- ✅ 与现有API完全兼容

## 基本用法

### 1. 单字段查询（原有功能）

```python
from utils.influxql_helper import InfluxQueryWrapper

# 单字段查询
qw = InfluxQueryWrapper("sensor_data", field="temperature")
query = qw.build()
# 生成: SELECT "temperature" FROM "sensor_data"
```

### 2. 多字段查询（新功能）

```python
# 多字段查询
qw = InfluxQueryWrapper("sensor_data", field=["temperature", "pressure", "humidity"])
query = qw.build()
# 生成: SELECT "temperature", "pressure", "humidity" FROM "sensor_data"
```

### 3. 多字段聚合查询

```python
# 多字段平均值查询
qw = InfluxQueryWrapper(
    "sensor_data", 
    field=["temperature", "pressure", "humidity"],
    aggregation="mean"
)
query = qw.build()
# 生成: SELECT MEAN("temperature") AS "temperature", MEAN("pressure") AS "pressure", MEAN("humidity") AS "humidity" FROM "sensor_data"
```

## 高级用法

### 1. 多测量点多字段查询

```python
# 查询多个传感器的多个字段
qw = InfluxQueryWrapper(
    "sensor_1", "sensor_2", "sensor_3",
    field=["temperature", "pressure"]
)
query = qw.build()
# 生成: SELECT "temperature", "pressure" FROM "sensor_1", "sensor_2", "sensor_3"
```

### 2. 带条件的多字段查询

```python
qw = InfluxQueryWrapper("production_line", field=["temperature", "pressure", "speed"])
qw.ge("time", "2025-01-01T00:00:00Z")
qw.le("time", "2025-01-01T23:59:59Z")
qw.gt("temperature", 100)
qw.eq("status", "running")
qw.order_by_desc("time")
qw.limit(1000)

query = qw.build()
```

### 3. 时间分组的多字段聚合

```python
qw = InfluxQueryWrapper(
    "process_monitor",
    field=["temperature", "pressure", "flow_rate"],
    aggregation="mean"
)
qw.group_by_time("1h", fill="linear")
qw.ge("time", "2025-01-01T00:00:00Z")
qw.le("time", "2025-01-07T23:59:59Z")

query = qw.build()
```

## 使用 batch_query_aligned_df 进行多字段查询

`batch_query_aligned_df` 方法已经更新以支持多字段查询：

```python
from utils.influxql_helper import InfluxQuery

# 创建连接
influx_query = InfluxQuery(host, port, username, password, database)

# 多字段查询
df = influx_query.batch_query_aligned_df(
    measurements=["sensor_1", "sensor_2"],
    field=["temperature", "pressure", "humidity"],  # 多字段
    start_time="2025-01-01T00:00:00Z",
    end_time="2025-01-01T23:59:59Z",
    time_interval="1min",
    fill_method="linear"
)

# 结果DataFrame的列名格式：
# - 单字段: "测量点名称"
# - 多字段: "测量点名称_字段名称"
print(df.columns.tolist())
# 例如: ['sensor_1_temperature', 'sensor_1_pressure', 'sensor_2_temperature', 'sensor_2_pressure']
```

## 支持的聚合函数

所有聚合函数都支持多字段操作：

- `mean`: 平均值
- `max`: 最大值
- `min`: 最小值
- `sum`: 求和
- `count`: 计数
- `median`: 中位数

```python
# 不同聚合函数示例
aggregations = ["mean", "max", "min", "sum", "count"]

for agg in aggregations:
    qw = InfluxQueryWrapper(
        "sensor_data",
        field=["temperature", "pressure"],
        aggregation=agg
    )
    query = qw.build()
    print(f"{agg}: {query}")
```

## 列名处理规则

### 单字段查询
- 列名格式：`测量点名称`
- 例如：`sensor_1`

### 多字段查询
- 列名格式：`测量点名称_字段名称`
- 例如：`sensor_1_temperature`, `sensor_1_pressure`

### 特殊情况
- 如果只有一个字段且只有一个测量点，使用测量点名称作为列名
- 如果查询结果中没有指定的字段，会自动使用所有数值列

## 兼容性说明

- ✅ 完全向后兼容：现有的单字段查询代码无需修改
- ✅ 所有现有方法（条件、排序、分组等）都支持多字段查询
- ✅ 现有的 `batch_query_aligned_df` 调用方式保持不变

## 实际应用示例

### 退火炉多参数监控

```python
# 同时监控多个退火炉的温度和压力
measurements = [
    "furnace_zone_1",
    "furnace_zone_2", 
    "furnace_zone_3"
]

df = influx_query.batch_query_aligned_df(
    measurements=measurements,
    field=["temperature", "pressure", "oxygen_level"],
    start_time="2025-01-01T08:00:00Z",
    end_time="2025-01-01T18:00:00Z",
    time_interval="1min",
    fill_method="linear"
)

# 结果包含所有炉区的所有参数
# 列名: furnace_zone_1_temperature, furnace_zone_1_pressure, furnace_zone_1_oxygen_level, ...
```

### 生产线质量分析

```python
# 分析生产线的多个质量指标
qw = InfluxQueryWrapper(
    "production_line_A",
    field=["temperature", "pressure", "speed", "quality_score", "defect_rate"],
    aggregation="mean"
)
qw.group_by_time("1h")
qw.ge("time", "2025-01-01T00:00:00Z")
qw.le("time", "2025-01-31T23:59:59Z")

# 获取每小时的平均值
df = influx_query.query_df_by_wrapper(qw)
```

## 注意事项

1. **性能考虑**：查询字段过多可能影响性能，建议根据实际需要选择字段
2. **内存使用**：多字段查询会增加内存使用，特别是在大时间范围查询时
3. **字段存在性**：确保查询的字段在数据库中存在，否则可能返回空结果
4. **列名冲突**：多字段查询时会自动处理列名冲突，使用"测量点_字段"格式

## 错误处理

```python
try:
    df = influx_query.batch_query_aligned_df(
        measurements=["sensor_1"],
        field=["temperature", "nonexistent_field"],
        start_time="2025-01-01T00:00:00Z",
        end_time="2025-01-01T01:00:00Z"
    )
    
    if df is None or df.empty:
        print("查询结果为空，请检查字段名称和时间范围")
    else:
        print(f"成功查询到 {df.shape[0]} 行数据")
        
except Exception as e:
    print(f"查询失败: {e}")
```

通过这些功能，您现在可以更高效地进行多维度数据分析，同时保持代码的简洁性和可读性。
